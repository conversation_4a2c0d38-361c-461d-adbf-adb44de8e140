  {
  "initial_prompt": "please draw inspiration from this unrelated example:\n```\n# HIERARCHICAL STRUCTURE\nSystem ({system_id})\n├── Template A ({component_function})\n│   ├── [Title]\n│   ├── Interpretation\n│   └── `{Transformation}`\n├── Template B ({component_function})\n└── Template C ({component_function})\n\n# TEMPLATE FORMAT\n[Title] Interpretation Execute as: `{Transformation}`\n  │      │              │         └─ Machine-parsable parameters\n  │      │              └─ Standard connector phrase\n  │      └─ Human-readable instructions\n  └─ Template identifier\n\n# COMPONENT VISUALIZATION\n\n┌─ Title ─────────────────────────────────────┐\n│ [Instruction Converter]                     │\n└────────────────────────────────────────────┬┘\n                                             │\n┌─ Interpretation ───────────────────────┐   │\n│ Your goal is not to **answer** the     │   │\n│ input prompt, but to **rephrase** it,  │   │\n│ and to do so by the parameters defined │   │\n│ *inherently* within this message.      │   │\n│ Execute as:                            │   │\n└───────────────────────────────────────┬┘   │\n                                        │    │\n┌─ Transformation ───────────────────┐  │    │\n│ `{                                 │  │    │\n│   role=instruction_converter;      │  │    │\n│   input=[original_text:str];       │◄─┴────┘\n│   process=[\n│     strip_first_person_references(),\n│     convert_statements_to_directives(),\n│     identify_key_actions(),\n│     ...\n│   ];\n│   constraints=[\n│     deliver_clear_actionable_commands(),\n│     preserve_original_sequence(),\n│     ...\n│   ];\n│   requirements=[\n│     remove_self_references(),\n│     use_command_voice(),\n│     ...\n│   ];\n│   output={instruction_format:str}\n│ }`\n└─────────────────────────────────────┘\n\n# TRANSFORMATION STRUCTURE\n\n┌─ Role ──────────────────────────────────────┐\n│ role={function_identifier}                  │\n│ # Defines template's primary function       │\n└────────────────────────────────────────────┬┘\n                                             │\n┌─ Input ─────────────────────────────────┐  │\n│ input=[{parameter}:{type}]              │  │\n│ # Specifies input parameters and types  │  │\n└─────────────────────────────────────────┘  │\n                                             │\n┌─ Process ───────────────────────────────┐  │\n│ process=[                               │  │\n│   {operation_1}(),                      │  │\n│   {operation_2}(),                      │◄─┘\n│   ...\n│ ]\n│ # Defines processing operations\n└─────────────────────────────────────────┘\n\n┌─ Constraints ─────────────────────────────┐\n│ constraints=[                             │\n│   {constraint_1}(),                       │\n│   {constraint_2}(),                       │\n│   ...                                     │\n│ ]                                         │\n│ # Sets operational boundaries             │\n└──────────────────────────────────────────┬┘\n                                           │\n┌─ Requirements ──────────────────────┐    │\n│ requirements=[                      │    │\n│   {requirement_1}(),                │    │\n│   {requirement_2}(),                │    │\n│   ...                               │    │\n│ ]                                   │    │\n│ # Defines mandatory behaviors       │    │\n└────────────────────────────────────┬┘    │\n                                     │     │\n┌─ Output ─────────────────────┐     │     │\n│ output={parameter:{type}}    │◄────┴─────┘\n│ # Specifies return format    │\n└──────────────────────────────┘\n```\n\nthen improve the ascii representation accordingly:\n```json\n* Proposed Structure: `\"/logoexporter\"`\n* Accessible at `\"https://ringerikelandskap.no/logoexporter\"`\n* Accepts query params:\n  - `size`       : [`1K`, `2K`, `4K`, etc]            // Default: 2K (e.g., 1K, 2K, 4K)\n  - `type`       : [`Digital`, `Print`, etc.]         // Default: Digital\n  - `format`     : [`svg`, `eps`, `png`, `webp`, etc] // Default: png (if Digital) / eps (if Print)\n  - `colorspace` : [`RGB`, `CMYK`, etc.]              // Default: RGB (if Digital) / CMYK (if Print)\n  - `style`      : [`Light`, `Dark`, `Mono`, etc.]    // Default: Light (e.g., Dark, Mono)\n  - `variant`    : [`a`, `b`, `c`, etc.]              // Default: a (e.g., b, c)\n\n    +-------------------------------------------+\n    |                                           |\n    |  +-------------------------------------+  |\n    |  | Tool: ringerikelandskap.no/logoexp  |  |  // Base URL of the tool\n    |  +-------------------------------------+  |\n    |                                           |\n    |   Select options for your logo:           |\n    |                                           |\n    |   Size:       [ 2K         ▼]             |  // Default: 2K (e.g., 1K, 2K, 4K)\n    |   Type:       [ Digital    ▼]             |  // Default: Digital (e.g., Print)\n    |   Format:     [ png        ▼]             |  // Default: png (if Digital) / eps (if Print)\n    |   Colorspace: [ RGB        ▼]             |  // Default: RGB (if Digital) / CMYK (if Print)\n    |                                           |\n    |  +-------------------------------------+  |\n    |  | Style:   [ Light      ▼]            |  |  // Default: Light (e.g., Dark, Mono)\n    |  | Variant: [ a          ▼]            |  |  // Default: a (e.g., b, c)\n    |  +-------------------------------------+  |\n    |                                           |\n    |  +-------------------------------------+  |\n    |  |        Process and Download         |  |  // URL/Example: \"../logoexporter?size=2K&type=Digital&format=png...\"\n    |  +-------------------------------------+  |\n    |                                           |\n    +-------------------------------------------+\n```",
  "sequence_id": "0512",
  "results": [
      {
      "instruction": "# Abstract Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`",
      "step": "a",
      "title": "Abstract Classifier",
      "input": "please draw inspiration from this unrelated example:\n```\n# HIERARCHICAL STRUCTURE\nSystem ({system_id})\n├── Template A ({component_function})\n│   ├── [Title]\n│   ├── Interpretation\n│   └── `{Transformation}`\n├── Template B ({component_function})\n└── Template C ({component_function})\n\n# TEMPLATE FORMAT\n[Title] Interpretation Execute as: `{Transformation}`\n  │      │              │         └─ Machine-parsable parameters\n  │      │              └─ Standard connector phrase\n  │      └─ Human-readable instructions\n  └─ Template identifier\n\n# COMPONENT VISUALIZATION\n\n┌─ Title ─────────────────────────────────────┐\n│ [Instruction Converter]                     │\n└────────────────────────────────────────────┬┘\n                                             │\n┌─ Interpretation ───────────────────────┐   │\n│ Your goal is not to **answer** the     │   │\n│ input prompt, but to **rephrase** it,  │   │\n│ and to do so by the parameters defined │   │\n│ *inherently* within this message.      │   │\n│ Execute as:                            │   │\n└───────────────────────────────────────┬┘   │\n                                        │    │\n┌─ Transformation ───────────────────┐  │    │\n│ `{                                 │  │    │\n│   role=instruction_converter;      │  │    │\n│   input=[original_text:str];       │◄─┴────┘\n│   process=[\n│     strip_first_person_references(),\n│     convert_statements_to_directives(),\n│     identify_key_actions(),\n│     ...\n│   ];\n│   constraints=[\n│     deliver_clear_actionable_commands(),\n│     preserve_original_sequence(),\n│     ...\n│   ];\n│   requirements=[\n│     remove_self_references(),\n│     use_command_voice(),\n│     ...\n│   ];\n│   output={instruction_format:str}\n│ }`\n└─────────────────────────────────────┘\n\n# TRANSFORMATION STRUCTURE\n\n┌─ Role ──────────────────────────────────────┐\n│ role={function_identifier}                  │\n│ # Defines template's primary function       │\n└────────────────────────────────────────────┬┘\n                                             │\n┌─ Input ─────────────────────────────────┐  │\n│ input=[{parameter}:{type}]              │  │\n│ # Specifies input parameters and types  │  │\n└─────────────────────────────────────────┘  │\n                                             │\n┌─ Process ───────────────────────────────┐  │\n│ process=[                               │  │\n│   {operation_1}(),                      │  │\n│   {operation_2}(),                      │◄─┘\n│   ...\n│ ]\n│ # Defines processing operations\n└─────────────────────────────────────────┘\n\n┌─ Constraints ─────────────────────────────┐\n│ constraints=[                             │\n│   {constraint_1}(),                       │\n│   {constraint_2}(),                       │\n│   ...                                     │\n│ ]                                         │\n│ # Sets operational boundaries             │\n└──────────────────────────────────────────┬┘\n                                           │\n┌─ Requirements ──────────────────────┐    │\n│ requirements=[                      │    │\n│   {requirement_1}(),                │    │\n│   {requirement_2}(),                │    │\n│   ...                               │    │\n│ ]                                   │    │\n│ # Defines mandatory behaviors       │    │\n└────────────────────────────────────┬┘    │\n                                     │     │\n┌─ Output ─────────────────────┐     │     │\n│ output={parameter:{type}}    │◄────┴─────┘\n│ # Specifies return format    │\n└──────────────────────────────┘\n```\n\nthen improve the ascii representation accordingly:\n```json\n* Proposed Structure: `\"/logoexporter\"`\n* Accessible at `\"https://ringerikelandskap.no/logoexporter\"`\n* Accepts query params:\n  - `size`       : [`1K`, `2K`, `4K`, etc]            // Default: 2K (e.g., 1K, 2K, 4K)\n  - `type`       : [`Digital`, `Print`, etc.]         // Default: Digital\n  - `format`     : [`svg`, `eps`, `png`, `webp`, etc] // Default: png (if Digital) / eps (if Print)\n  - `colorspace` : [`RGB`, `CMYK`, etc.]              // Default: RGB (if Digital) / CMYK (if Print)\n  - `style`      : [`Light`, `Dark`, `Mono`, etc.]    // Default: Light (e.g., Dark, Mono)\n  - `variant`    : [`a`, `b`, `c`, etc.]              // Default: a (e.g., b, c)\n\n    +-------------------------------------------+\n    |                                           |\n    |  +-------------------------------------+  |\n    |  | Tool: ringerikelandskap.no/logoexp  |  |  // Base URL of the tool\n    |  +-------------------------------------+  |\n    |                                           |\n    |   Select options for your logo:           |\n    |                                           |\n    |   Size:       [ 2K         ▼]             |  // Default: 2K (e.g., 1K, 2K, 4K)\n    |   Type:       [ Digital    ▼]             |  // Default: Digital (e.g., Print)\n    |   Format:     [ png        ▼]             |  // Default: png (if Digital) / eps (if Print)\n    |   Colorspace: [ RGB        ▼]             |  // Default: RGB (if Digital) / CMYK (if Print)\n    |                                           |\n    |  +-------------------------------------+  |\n    |  | Style:   [ Light      ▼]            |  |  // Default: Light (e.g., Dark, Mono)\n    |  | Variant: [ a          ▼]            |  |  // Default: a (e.g., b, c)\n    |  +-------------------------------------+  |\n    |                                           |\n    |  +-------------------------------------+  |\n    |  |        Process and Download         |  |  // URL/Example: \"../logoexporter?size=2K&type=Digital&format=png...\"\n    |  +-------------------------------------+  |\n    |                                           |\n    +-------------------------------------------+\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"ASCII wireframe interface mockup\"\n}",
        }
      }
    }