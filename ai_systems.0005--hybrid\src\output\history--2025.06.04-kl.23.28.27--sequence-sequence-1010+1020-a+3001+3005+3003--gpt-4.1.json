  {
  "initial_prompt": "Subject: No human subjects present in this mystical forest scene.\n\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\n\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.",
  "sequence_id": "1010|1020:a|3001|3005|3003",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "Subject: No human subjects present in this mystical forest scene.\n\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\n\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Ethereal Monochromatic Green Fantasy Forest Ravine With Mist and Moss-Covered Stream\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Subject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Ethereal Monochromatic Green Fantasy Forest Ravine With Mist and Moss-Covered Stream\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Mystical Emerald Forest Ravine With Mist\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Subject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Mystical Emerald Forest Ravine With Mist\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Mystical Emerald Forest Ravine\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Subject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Mystical Emerald Forest Ravine\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Forest Ravine\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Subject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Forest Ravine\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"generateEtherealFantasyForestSceneWithMonochromaticGreenMist\"\n}",
        }
      }
    },
      {
      "instruction": "# Bracketed Keyword Infuser\n\nYour goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as:\n\n`{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`",
      "step": "a",
      "title": "Bracketed Keyword Infuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Subject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"function_name\": \"generateEtherealFantasyForestSceneWithMonochromaticGreenMist\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_description\": \"[scene][no_human_subjects] A winding stream cuts through [moss-covered_rocks] and [emerald_grass], creating a [serpentine_path] through a [deep_forest_ravine][framing:wide_angle]. [Towering_trees][bare_branches] emerge from [steep, moss-blanketed_slopes][angle:low], while a [thick_mist][depth_of_field:shallow][background] hovers in the distance, creating [layers_of_depth] through the gorge. [lighting:soft_diffused][atmospheric_fog][color_palette:monochromatic_green][tones:emerald][texture:highlighted_moss_and_water] The [ethereal][dreamy][mystical][atmospheric][fantasy_photography] mood is intensified by [camera_movement:slow_push_forward] into the gorge, letting [fog][mist] and [textural_details] emerge gradually as [lighting] filters through the [canopy], emphasizing the [mysterious_ambience].\"\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Pattern Analyzer\n\nYour goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as:\n\n`{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`",
      "step": "a",
      "title": "Syntactic Pattern Analyzer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Subject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"enhanced_description\": \"[scene][no_human_subjects] A winding stream cuts through [moss-covered_rocks] and [emerald_grass], creating a [serpentine_path] through a [deep_forest_ravine][framing:wide_angle]. [Towering_trees][bare_branches] emerge from [steep, moss-blanketed_slopes][angle:low], while a [thick_mist][depth_of_field:shallow][background] hovers in the distance, creating [layers_of_depth] through the gorge. [lighting:soft_diffused][atmospheric_fog][color_palette:monochromatic_green][tones:emerald][texture:highlighted_moss_and_water] The [ethereal][dreamy][mystical][atmospheric][fantasy_photography] mood is intensified by [camera_movement:slow_push_forward] into the gorge, letting [fog][mist] and [textural_details] emerge gradually as [lighting] filters through the [canopy], emphasizing the [mysterious_ambience].\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Begin with a subject/reference section (e.g., 'Subject: ...')\",\n      \"Arrange content in separated thematic blocks (e.g., Scene, Style)\",\n      \"Use paragraph or line breaks to distinguish sections (e.g., newlines between Scene and Style)\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Bracket-enclosed identifiers (e.g., [scene], [no_human_subjects], [lighting:soft_diffused]) precede or annotate descriptive segments\",\n      \"Use of compound, hyphen-joined, or underscore-separated tokens for conceptual descriptors (e.g., moss-covered_rocks, monochromatic_green, camera_movement:slow_push_forward)\"\n    ],\n    \"map_syntax_rules\": [\n      \"Descriptive narrative constructed by positioning physical elements and their attributes in sequence\",\n      \"Attribute blocks may be attached to noun phrases for added context or visual parameters\",\n      \"Formatting divides scene description and stylistic/technical details\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Section headers (Scene:, Style:) optionally followed by newlines\",\n      \"Brackets used for tags/parameters ([tag], [parameter:value])\",\n      \"Use of punctuation (colon, comma, period) to separate lists, attributes, or clause boundaries\"\n    ],\n    \"define_parameter_structures\": [\n      \"Parameter annotation format: [category:parameter_value]\",\n      \"Standalone tags: [descriptor]\",\n      \"Stacking multiple tags/parameters preceding or inserted into sentences or phrases\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"Convert prose or narrative blocks into bracket-tagged syntax highlighting scene elements, properties, and modifiers\",\n      \"Represent structural relationships (location, atmosphere, mood, camera movement) with tagged parameters in consistent positions\",\n      \"Maintain a modular narrative flow mapped to visual or compositional categories\"\n    ],\n    \"codify_transformation_rules\": [\n      \"Replace descriptive or narrative core components with bracketed tags denoting roles, properties, and modifiers ([object][attribute])\",\n      \"For every major object or environmental property, prepend, append, or insert bracketed tags specifying its role, appearance, or compositional purpose\",\n      \"Transitions between elements (sentences or clauses) can serve as breakpoints to inject or stack further bracketed tags\",\n      \"Use colon-separated parameterization ([type:variant]) for customizable attributes or visual styles\",\n      \"For style, mood, or camera/lighting/setup instructions, aggregate relevant bracketed tags at the end or inserted inline at natural syntactic divisions\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Prompt Builder\n\nYour goal is not to **describe** visual concepts generally, but to **construct** single-line prompts under 500 characters using bracketed keyword syntax [keyword] and [keyword:value], emphasis markers **text**, and vivid descriptive language to create visual transformation sequences. Execute as:\n\n`{role=syntactic_prompt_builder; input=[visual_concept:str]; process=[identify_transformation_elements(), select_bracketed_keywords(), apply_parameterized_directives(), integrate_emphasis_markers(), construct_vivid_descriptive_text(), ensure_syntactic_compliance(), validate_character_limit(), format_single_line_output()]; constraints=[under_500_character_limit(), bracketed_keyword_syntax_only(), emphasis_marker_integration(), single_line_format(), vivid_verb_requirement()]; requirements=[syntactic_structure_compliance(), visual_transformation_clarity(), keyword_parameter_integration(), descriptive_flow_maintenance()]; output={structured_prompt:str}}`",
      "step": "a",
      "title": "Syntactic Prompt Builder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Subject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Begin with a subject/reference section (e.g., 'Subject: ...')\",\n      \"Arrange content in separated thematic blocks (e.g., Scene, Style)\",\n      \"Use paragraph or line breaks to distinguish sections (e.g., newlines between Scene and Style)\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Bracket-enclosed identifiers (e.g., [scene], [no_human_subjects], [lighting:soft_diffused]) precede or annotate descriptive segments\",\n      \"Use of compound, hyphen-joined, or underscore-separated tokens for conceptual descriptors (e.g., moss-covered_rocks, monochromatic_green, camera_movement:slow_push_forward)\"\n    ],\n    \"map_syntax_rules\": [\n      \"Descriptive narrative constructed by positioning physical elements and their attributes in sequence\",\n      \"Attribute blocks may be attached to noun phrases for added context or visual parameters\",\n      \"Formatting divides scene description and stylistic/technical details\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Section headers (Scene:, Style:) optionally followed by newlines\",\n      \"Brackets used for tags/parameters ([tag], [parameter:value])\",\n      \"Use of punctuation (colon, comma, period) to separate lists, attributes, or clause boundaries\"\n    ],\n    \"define_parameter_structures\": [\n      \"Parameter annotation format: [category:parameter_value]\",\n      \"Standalone tags: [descriptor]\",\n      \"Stacking multiple tags/parameters preceding or inserted into sentences or phrases\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"Convert prose or narrative blocks into bracket-tagged syntax highlighting scene elements, properties, and modifiers\",\n      \"Represent structural relationships (location, atmosphere, mood, camera movement) with tagged parameters in consistent positions\",\n      \"Maintain a modular narrative flow mapped to visual or compositional categories\"\n    ],\n    \"codify_transformation_rules\": [\n      \"Replace descriptive or narrative core components with bracketed tags denoting roles, properties, and modifiers ([object][attribute])\",\n      \"For every major object or environmental property, prepend, append, or insert bracketed tags specifying its role, appearance, or compositional purpose\",\n      \"Transitions between elements (sentences or clauses) can serve as breakpoints to inject or stack further bracketed tags\",\n      \"Use colon-separated parameterization ([type:variant]) for customizable attributes or visual styles\",\n      \"For style, mood, or camera/lighting/setup instructions, aggregate relevant bracketed tags at the end or inserted inline at natural syntactic divisions\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structured_prompt\": \"[no_human_subjects][scene][mystical_forest][stream:serpentine][moss_covered_rocks][emerald_grass][ravine:deep][trees:towering][branches:bare][slopes:moss_blanketed][mist:thick,hovering][depth:layered][fog:atmospheric][lighting:soft_diffused][monochromatic:green][palette:emerald][mood:dreamy,ethereal,mystical][texture:moss,water][highlighting:texture] **A serpentine stream slices through emerald grass and moss-clad rocks, fog-laden air swirling amid towering, bare-branched trees on steep, verdant slopes; soft, diffused light caresses every ethereal detail, conjuring an enchanting, mystical mood.**\"\n}",
        }
      }
    }