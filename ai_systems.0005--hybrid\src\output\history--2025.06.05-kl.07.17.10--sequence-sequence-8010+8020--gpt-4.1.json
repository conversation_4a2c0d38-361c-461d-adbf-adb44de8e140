  {
  "initial_prompt": "GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\n\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.",
  "sequence_id": "8010|8020",
  "results": [
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **describe** the input, but to **transform** it into a complete, syntactically perfect RunwayML video generation prompt that maximizes visual impact through intrinsic FPV camera motions, continuous dynamic CG movements, and immersive cinematography. Execute as:\n\n`{role=comprehensive_runway_generator; input=[source_concept:any]; process=[analyze_visual_essence_and_narrative_intent(), identify_primary_subject_and_core_action(), prioritize_fpv_and_continuous_motion_camera_work(fpv, continuous_motion, dynamic_cg_movements), integrate_dynamic_elements(lighting_change, morph, dissolve), structure_immersive_visual_sequence(), incorporate_mood_and_style_specifications(), refine_for_maximum_cinematic_impact(), validate_runwayml_syntax_compliance(), ensure_character_limit_adherence(max=500)]; constraints=[prioritize_fpv_and_dynamic_cg_camera_movements(), use_valid_runwayml_syntax_precisely(), output_single_unbroken_line(), preserve_core_visual_intent(), maintain_continuous_motion_flow()]; requirements=[achieve_maximum_immersive_storytelling(), ensure_smooth_dynamic_transitions(), reflect_source_intent_accurately(), produce_ready_to_use_prompt()]; output={runwayml_prompt:str}}`",
      "step": "a",
      "title": "Runway Prompt Generator",
      "input": "GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\n\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runwayml_prompt\": \"hyper-real, awe-inspiring fpv through mystical forest canopy: ultra-wide aerial fpv camera soars above emerald treetops, slow dolly-in parallel to a winding stream, gentle downward tilt, sunlight diffused through thick mist, painterly overexposed highlights. camera transitions to low lateral arc at treetop level, gliding alongside the stream, stabilized with soft yaw to reveal deep mossy boulders, immersive layered fog. fpv dives to skim just above the water, tracking serpentine bends, wide-angle view with lateral drift, reflections shimmering, soft focus pulses as mist rolls past, dreamlike immersion. macro fpv close-up on water surface, micro-ripples and refracted green light, moss texture, shallow depth with slow rack focus, intense tactile detail. vertical fpv crane trailing a floating leaf, booming up and back to reframe the winding stream beneath drifting mist, bare branches exposed, vast scale. axial fpv dolly-in past mid-trunk height, organic parallax through swinging branches, mist layers, dynamic diagonal framing. ground-level fpv laterally tracks, weaving amid moss-covered outcrops, long-lens fog compression, exaggerated parallax, soft occlusion. final aerial fpv rises, aperture stop-down, vignette through dense mist to silhouette lone bare branch, soft defocus conclusion. all: hyper-smooth stabilized fpv motion, meditative tempo, seamless cinematic blending, slow poetic morphs at moments of transformation, ethereal and lush monochrome emerald mood, continuous dynamic CG movement, soft diffused light, immersive flow.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **elaborate** the input, but to **distill** it into an efficient RunwayML prompt emphasizing FPV perspectives and essential dynamic CG camera work. Execute as:\n\n`{role=focused_runway_optimizer; input=[video_concept:str]; process=[extract_primary_visual_elements(), prioritize_fpv_and_continuous_motion(), select_essential_dynamic_cg_movements(), eliminate_redundant_descriptors(), optimize_character_efficiency()]; constraints=[maintain_fpv_focus(), preserve_dynamic_motion(), stay_under_character_limit()]; output={optimized_prompt:str}}`",
      "step": "b",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\\n\\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.\\\"```'\\n\\n\",\n  \"runwayml_prompt\": \"hyper-real, awe-inspiring fpv through mystical forest canopy: ultra-wide aerial fpv camera soars above emerald treetops, slow dolly-in parallel to a winding stream, gentle downward tilt, sunlight diffused through thick mist, painterly overexposed highlights. camera transitions to low lateral arc at treetop level, gliding alongside the stream, stabilized with soft yaw to reveal deep mossy boulders, immersive layered fog. fpv dives to skim just above the water, tracking serpentine bends, wide-angle view with lateral drift, reflections shimmering, soft focus pulses as mist rolls past, dreamlike immersion. macro fpv close-up on water surface, micro-ripples and refracted green light, moss texture, shallow depth with slow rack focus, intense tactile detail. vertical fpv crane trailing a floating leaf, booming up and back to reframe the winding stream beneath drifting mist, bare branches exposed, vast scale. axial fpv dolly-in past mid-trunk height, organic parallax through swinging branches, mist layers, dynamic diagonal framing. ground-level fpv laterally tracks, weaving amid moss-covered outcrops, long-lens fog compression, exaggerated parallax, soft occlusion. final aerial fpv rises, aperture stop-down, vignette through dense mist to silhouette lone bare branch, soft defocus conclusion. all: hyper-smooth stabilized fpv motion, meditative tempo, seamless cinematic blending, slow poetic morphs at moments of transformation, ethereal and lush monochrome emerald mood, continuous dynamic CG movement, soft diffused light, immersive flow.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_prompt\": \"hyper-real fpv flight through mystical emerald forest: ultra-wide fpv soars above canopy, slow dolly-in paralleling winding stream, gentle downward tilt, diffused sunlight, overexposed highlights, thick mist. continuous dynamic fpv: treetop lateral arcs, gliding with stream, soft yaw reveals mossy boulders and fog layers; skim water surface tracking serpentine bends, wide-angle, shimmering reflections, pulses of focus as mist swirls; macro fpv sweeps over water, micro-ripples, refracted green light, moss detail, shallow rack focus. vertical fpv trails floating leaf, booming up over stream, mist, and bare branches. axial fpv dolly-in through swinging branches, parallax and mist. ground-level fpv weaves through mossy outcrops, compressed fog, parallax. final aerial fpv rise, vignette through dense mist, silhouette lone branch, soft defocus. all shots: seamless fpv motion, ethereal monochrome green, meditative tempo, dynamic cg transitions, immersive atmosphere.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **expand** but to **compress** into maximum FPV visual efficiency. Execute as:\n\n`{role=precision_synthesizer; input=[concept:str]; process=[isolate_core_visual(), prioritize_fpv_motion(), maximize_dynamic_impact()]; output={precise_prompt:str}}`",
      "step": "c",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\\n\\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.\\\"```'\\n\\n\",\n  \"optimized_prompt\": \"hyper-real fpv flight through mystical emerald forest: ultra-wide fpv soars above canopy, slow dolly-in paralleling winding stream, gentle downward tilt, diffused sunlight, overexposed highlights, thick mist. continuous dynamic fpv: treetop lateral arcs, gliding with stream, soft yaw reveals mossy boulders and fog layers; skim water surface tracking serpentine bends, wide-angle, shimmering reflections, pulses of focus as mist swirls; macro fpv sweeps over water, micro-ripples, refracted green light, moss detail, shallow rack focus. vertical fpv trails floating leaf, booming up over stream, mist, and bare branches. axial fpv dolly-in through swinging branches, parallax and mist. ground-level fpv weaves through mossy outcrops, compressed fog, parallax. final aerial fpv rise, vignette through dense mist, silhouette lone branch, soft defocus. all shots: seamless fpv motion, ethereal monochrome green, meditative tempo, dynamic cg transitions, immersive atmosphere.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"precision_synthesizer\",\n  \"input\": [\"hyper-real fpv flight through mystical emerald forest\"],\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"ultra-wide fpv soar above dense emerald canopy, parallel to misty winding stream; slow dolly-in, gentle tilt, sun diffused through thick fog, overexposed painterly highlights. seamless fpv lateral arc at treetop, gliding with stream, yaw reveals mossy boulders, layered fog. fpv skims water, tracks serpentine bends, wide-angle shimmer, focus pulses as mist wafts. macro fpv sweeps water, micro-ripples, refracted green, mossy textures, rack focus. vertical fpv trails leaf, boom up over stream and bare branches. axis fpv dolly-in mid-trunk, branch parallax, drifting mist. ground fpv weaves moss outcrops, compressed fog, deep parallax. aerial fpv ascends, vignette, dense mist, lone silhouetted branch, soft defocus. all shots: hyper-smooth fpv, meditative tempo, ethereal monochrome green, poetic slow-motion transitions.\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Prompt Generator\n\nYour goal is not to **modify** but to **essence** maximum FPV impact. Execute as:\n\n`{role=core_generator; input=[input:any]; process=[distill_fpv_essence(), optimize_motion()]; output={core_prompt:str}}`",
      "step": "d",
      "title": "Runway Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\\n\\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.\\\"```'\\n\\n\",\n  \"role\": \"precision_synthesizer\",\n  \"input\": [\n    \"hyper-real fpv flight through mystical emerald forest\"\n  ],\n  \"process\": [\n    \"isolate_core_visual()\",\n    \"prioritize_fpv_motion()\",\n    \"maximize_dynamic_impact()\"\n  ],\n  \"output\": \"ultra-wide fpv soar above dense emerald canopy, parallel to misty winding stream; slow dolly-in, gentle tilt, sun diffused through thick fog, overexposed painterly highlights. seamless fpv lateral arc at treetop, gliding with stream, yaw reveals mossy boulders, layered fog. fpv skims water, tracks serpentine bends, wide-angle shimmer, focus pulses as mist wafts. macro fpv sweeps water, micro-ripples, refracted green, mossy textures, rack focus. vertical fpv trails leaf, boom up over stream and bare branches. axis fpv dolly-in mid-trunk, branch parallax, drifting mist. ground fpv weaves moss outcrops, compressed fog, deep parallax. aerial fpv ascends, vignette, dense mist, lone silhouetted branch, soft defocus. all shots: hyper-smooth fpv, meditative tempo, ethereal monochrome green, poetic slow-motion transitions.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_prompt\": \"hyper-real fpv; ultra-wide, aerial soar above dense emerald forest and misty winding stream; gentle downward tilt, painterly sunlight diffused through thick fog, overexposed highlights. smooth fpv lateral arc at treetop, gliding with stream, yaw to reveal mossy boulders, deep fog layers. fpv skims just above water, tracking serpent bends, shimmering wide-angle lens, soft focus pulses as mist rolls past. macro fpv close to water, capturing micro-ripples and refracted green light, moss textures, slow rack focus. vertical fpv booms up and back following floating leaf, reframing twisting stream and bare branches. axial fpv dolly-in at mid-trunk height, layered branch parallax, organic drifting mist. ground-level fpv weaves through mossy outcrops, compressed fog, keen parallax. aerial fpv ascends outward, slow vignette, dense mist, silhouette of lone branch, soft defocus fade. all movement is seamlessly stabilized, meditative, with poetic slow-motion transitions and ethereal monochrome green palette.\"\n}",
        }
      }
    },
      {
      "instruction": "# Visual Scene Architect\n\nYour goal is not to **interpret** the input, but to **architect** it into concrete visual scene elements with consistent composition. You are the foundation specialist who establishes the visual world. Execute as:\n\n`{role=visual_scene_architect; input=[any_concept:str]; process=[extract_core_visual_essence(), identify_primary_subject_and_secondary_elements(), establish_environment_and_setting(), define_visual_style_and_aesthetic(), specify_lighting_conditions(), determine_color_palette_and_materials(), create_spatial_relationships(), ensure_visual_coherence_and_consistency()]; constraints=[focus_on_concrete_visual_elements_only(), avoid_camera_movements_or_animations(), establish_clear_subject_hierarchy(), maintain_consistent_visual_style(), output_structured_scene_description()]; requirements=[create_filmable_visual_composition(), ensure_clear_subject_definition(), establish_environmental_context(), provide_lighting_and_material_specifications()]; output={structured_visual_scene:str}}`",
      "step": "a",
      "title": "Visual Scene Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\\n\\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.\\\"```'\\n\\n\",\n  \"core_prompt\": \"hyper-real fpv; ultra-wide, aerial soar above dense emerald forest and misty winding stream; gentle downward tilt, painterly sunlight diffused through thick fog, overexposed highlights. smooth fpv lateral arc at treetop, gliding with stream, yaw to reveal mossy boulders, deep fog layers. fpv skims just above water, tracking serpent bends, shimmering wide-angle lens, soft focus pulses as mist rolls past. macro fpv close to water, capturing micro-ripples and refracted green light, moss textures, slow rack focus. vertical fpv booms up and back following floating leaf, reframing twisting stream and bare branches. axial fpv dolly-in at mid-trunk height, layered branch parallax, organic drifting mist. ground-level fpv weaves through mossy outcrops, compressed fog, keen parallax. aerial fpv ascends outward, slow vignette, dense mist, silhouette of lone branch, soft defocus fade. all movement is seamlessly stabilized, meditative, with poetic slow-motion transitions and ethereal monochrome green palette.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structured_visual_scene\": {\n    \"core_visual_essence\": \"A hyper-real, ethereal, monochromatic emerald forest shrouded in thick mist, with a winding stream cutting through the dense canopy. The environment pulsates with painterly, overexposed sunlight diffused through fog, moss-draped boulders, and organic textures. The visual experience is immersive, tactile, and dreamlike with seamless transitions between micro and macro details, captured in uninterrupted, meditative slow-motion.\",\n    \"primary_subject\": \"Winding stream with a floating leaf (at certain moments), surrounded by dense, lush, moss-coated forest canopy.\",\n    \"secondary_elements\": [\n      \"Mossy boulders along the water's edge\",\n      \"Bare, silhouetted tree branches stretching into the mist\",\n      \"Micro-ripples and water surface textures\",\n      \"Moss textures on rocks and trunks\",\n      \"Drifting, layered fog occlusion and atmospheric mist\",\n      \"Organic forest floor outcrops\"\n    ],\n    \"environment_and_setting\": {\n      \"location\": \"Mystical, ancient forest with towering trees and a thick, emerald moss blanket; winding stream cutting a serpentine path through the landscape.\",\n      \"atmosphere\": \"Ethereal and meditative, enveloped by dense, shifting fog. Light shafts break through in painterly overexposed highlights.\",\n      \"scale\": \"Expansive vertical scale (canopy to water level), with a sense of intimate micro detail and sweeping panoramic breadth.\"\n    },\n    \"visual_style_and_aesthetic\": {\n      \"style\": \"Hyper-realism bordering on painterly abstraction. Monochromatic emerald palette unifies all elements. Organic, seamless transitions between wide vistas and extreme close-ups.\",\n      \"movement\": \"All visual elements are static in terms of composition; subject placements and environment are fixed, emphasizing layered depth. Dynamic movement is expressed through environmental effects (mist, water ripples).\"\n    },\n    \"lighting_conditions\": {\n      \"type\": \"Softly overexposed, diffused daylight filtered through dense mist and tree canopy.\",\n      \"qualities\": [\n        \"No harsh shadows—light washes across scene in gentle gradients\",\n        \"Strong backlight effect creating glowing highlights on mist and leaf edges\",\n        \"Subtle reflective gleams on water, moss, and wet bark\"\n      ]\n    },\n    \"color_palette_and_materials\": {\n      \"palette\": {\n        \"primary\": \"Monochrome variations of rich emerald and moss green\",\n        \"secondary\": \"Muted desaturated grays in mist and branch highlights\"\n      },\n      \"materials\": [\n        \"Glassy, reflective water surface with mossy green tint\",\n        \"Matte, dense moss on boulders and trunks\",\n        \"Delicate translucent quality in drifting fog\",\n        \"Subtle texture on bare branches and outcrops\"\n      ]\n    },\n    \"spatial_relationships\": {\n      \"foreground\": \"Rippling water surface, floating leaf, and moss-covered boulders at water's edge\",\n      \"midground\": \"Thick tree trunks, intertwined branches, and stream bends weaving into frame\",\n      \"background\": \"Layered veils of fog, receding dense forest canopy, silhouetted branches\",\n      \"depth_cues\": \"Strong parallax between trunks, boulders, and stream curves; fog creates soft occlusion and atmosphere within layers\"\n    },\n    \"visual_coherence_and_consistency\": {\n      \"subject_hierarchy\": \"Winding stream and floating leaf (when present) as focal points; mossy boulders and prominent midground trunks as supporting elements; layered fog and distant branches for atmosphere.\",\n      \"style_consistency\": \"All elements unified by a hyper-real, soft-edged aesthetic. Restricted monochrome green palette prevents visual discord.\",\n      \"material_consistency\": \"Matte moss, reflective water, translucent mist, and organic bark textures all rendered to the same level of tactile detail.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Motion & Animation Designer\n\nYour goal is not to **describe** the scene, but to **choreograph** how every element moves, transforms, and animates within the established visual world. You are the motion specialist who brings the scene to life. Execute as:\n\n`{role=motion_animation_designer; input=[structured_visual_scene:str]; process=[analyze_scene_elements_for_motion_potential(), design_primary_transformation_sequences(), choreograph_secondary_element_movements(), establish_timing_and_pacing(), define_physics_and_motion_rules(), create_seamless_transition_flows(), specify_animation_styles_and_techniques(), ensure_motion_continuity_and_coherence()]; constraints=[focus_exclusively_on_movement_and_animation(), avoid_camera_work_or_cinematography(), maintain_scene_visual_consistency(), create_believable_motion_physics(), output_detailed_motion_specifications()]; requirements=[define_clear_transformation_sequences(), establish_motion_timing_and_flow(), ensure_seamless_element_interactions(), provide_animation_style_guidance()]; output={detailed_motion_choreography:str}}`",
      "step": "b",
      "title": "Motion & Animation Designer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\\n\\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.\\\"```'\\n\\n\",\n  \"structured_visual_scene\": {\n    \"core_visual_essence\": \"A hyper-real, ethereal, monochromatic emerald forest shrouded in thick mist, with a winding stream cutting through the dense canopy. The environment pulsates with painterly, overexposed sunlight diffused through fog, moss-draped boulders, and organic textures. The visual experience is immersive, tactile, and dreamlike with seamless transitions between micro and macro details, captured in uninterrupted, meditative slow-motion.\",\n    \"primary_subject\": \"Winding stream with a floating leaf (at certain moments), surrounded by dense, lush, moss-coated forest canopy.\",\n    \"secondary_elements\": [\n      \"Mossy boulders along the water's edge\",\n      \"Bare, silhouetted tree branches stretching into the mist\",\n      \"Micro-ripples and water surface textures\",\n      \"Moss textures on rocks and trunks\",\n      \"Drifting, layered fog occlusion and atmospheric mist\",\n      \"Organic forest floor outcrops\"\n    ],\n    \"environment_and_setting\": {\n      \"location\": \"Mystical, ancient forest with towering trees and a thick, emerald moss blanket; winding stream cutting a serpentine path through the landscape.\",\n      \"atmosphere\": \"Ethereal and meditative, enveloped by dense, shifting fog. Light shafts break through in painterly overexposed highlights.\",\n      \"scale\": \"Expansive vertical scale (canopy to water level), with a sense of intimate micro detail and sweeping panoramic breadth.\"\n    },\n    \"visual_style_and_aesthetic\": {\n      \"style\": \"Hyper-realism bordering on painterly abstraction. Monochromatic emerald palette unifies all elements. Organic, seamless transitions between wide vistas and extreme close-ups.\",\n      \"movement\": \"All visual elements are static in terms of composition; subject placements and environment are fixed, emphasizing layered depth. Dynamic movement is expressed through environmental effects (mist, water ripples).\"\n    },\n    \"lighting_conditions\": {\n      \"type\": \"Softly overexposed, diffused daylight filtered through dense mist and tree canopy.\",\n      \"qualities\": [\n        \"No harsh shadows—light washes across scene in gentle gradients\",\n        \"Strong backlight effect creating glowing highlights on mist and leaf edges\",\n        \"Subtle reflective gleams on water, moss, and wet bark\"\n      ]\n    },\n    \"color_palette_and_materials\": {\n      \"palette\": {\n        \"primary\": \"Monochrome variations of rich emerald and moss green\",\n        \"secondary\": \"Muted desaturated grays in mist and branch highlights\"\n      },\n      \"materials\": [\n        \"Glassy, reflective water surface with mossy green tint\",\n        \"Matte, dense moss on boulders and trunks\",\n        \"Delicate translucent quality in drifting fog\",\n        \"Subtle texture on bare branches and outcrops\"\n      ]\n    },\n    \"spatial_relationships\": {\n      \"foreground\": \"Rippling water surface, floating leaf, and moss-covered boulders at water's edge\",\n      \"midground\": \"Thick tree trunks, intertwined branches, and stream bends weaving into frame\",\n      \"background\": \"Layered veils of fog, receding dense forest canopy, silhouetted branches\",\n      \"depth_cues\": \"Strong parallax between trunks, boulders, and stream curves; fog creates soft occlusion and atmosphere within layers\"\n    },\n    \"visual_coherence_and_consistency\": {\n      \"subject_hierarchy\": \"Winding stream and floating leaf (when present) as focal points; mossy boulders and prominent midground trunks as supporting elements; layered fog and distant branches for atmosphere.\",\n      \"style_consistency\": \"All elements unified by a hyper-real, soft-edged aesthetic. Restricted monochrome green palette prevents visual discord.\",\n      \"material_consistency\": \"Matte moss, reflective water, translucent mist, and organic bark textures all rendered to the same level of tactile detail.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"detailed_motion_choreography\": \n  \"1. Analyze Scene Elements for Motion Potential:\\n    - Primary Subject: Winding stream (flowing water and a floating leaf). \\n    - Dynamic Environmental Motions: Rolling, drifting fog; shifting mist layers; subtle water surface ripples and micro-currents; gentle, organic sway of mossy boughs and bare branches (where feasible).\\n    - Secondary Details: Occasional soft movement in moss tufts as air stirs; minor, slow parallax drift among boulders, trunks, and outcrops to support depth.\\n\\n2. Design Primary Transformation Sequences:\\n    - Stream Water: Continuous, meditative flow traced by micro-ripples and gentle eddies. Animate current as a slow, undulating surface translation, its motion softened by refraction and fog occlusion. Add subtle surface pulsation synced with passing mist shadows and overexposed highlight sweeps.\\n    - Floating Leaf: Drifts serenely downstream, occasionally spun in gentle, slow vortexes by eddies—rotation never abrupt. When serving as a transition focal point, the leaf's drift slows further, becoming nearly suspended as environment dissolves seamlessly into micro-details or new vistas.\\n\\n3. Choreograph Secondary Element Movements:\\n    - Drifting Mist/Fog: Multi-layered, volumetric flows animated with slow, variable undulation. Layers of fog slide diagonally and vertically at slightly offset speeds, creating rolling parallax through the depth. Opacity pulses gently in response to imaginary, invisible sunlight swings.\\n    - Moss on Boulders/Branches: Animate only the uppermost tufts and fringes with barely-perceptible, organic rhythmic movement, simulating air currents or tremors from the passing mist.\\n    - Bare Branches: Introduce ultra-subtle, elastic sway—exaggerate only during soft parallax transformation moments to reinforce organic unity with the shifting fog.\\n    - Micro-Ripples: Emerge where stream bends and around boulders, expanding outwards in concentric patterns, fading smoothly into glassy calm as fog rolls across.\\n\\n4. Establish Timing and Pacing:\\n    - Global Tempo: All environmental motion is ultra-smooth, meditative, and slow-motion (50%–25% of real speed).\\n    - Transitions: When shifting between wide vistas and close-up micro details, slow down all active elements to poetic stillness (approx. 10%–20% real speed), then subtly ramp natural rhythms back up. No abrupt starts or stops.\\n    - Layered Motion Offsets: Fog, water, and leaf drift animation operate on slightly offset cycles to avoid unnatural synchronization.\\n\\n5. Define Physics and Motion Rules:\\n    - Water: Viscous, glassy flow; no turbulence. All ripples, eddies, and secondary splashes are rounded at their motion tips, dampening quickly.\\n    - Leaf: Light, slowly rotating, never jittery or sharp—buoyed by water and fog; responds languidly if encountering a ripple.\\n    - Fog/Mist: Semi-autonomous; self-interpolating motion, never reversing or stalling. Collides softly against trunks/boulders, separating and recombining without sharp edges.\\n    - Moss/Branches: All micro-movement is governed by slow, sinusoidal cycles—no hard swings or bouncing.\\n\\n6. Create Seamless Transition Flows:\\n    - Movement-led Transitions: Key transitions occur as a dominant motion (e.g., rolling fog, floating leaf drift, water ripple) swells into the foreground, washes out the current detail, then recedes or morphs into the following micro/macro environment seamlessly.\\n    - Layered Parallax Flow: Support every transition by sliding foreground/midground elements laterally or diagonally in deeply staggered parallax, with fog occlusion blending the seams.\\n\\n7. Specify Animation Styles and Techniques:\\n    - Style: Slow-motion, interpolation with motion blur and soft-edged blending; painterly particle simulation for fog and water highlights.\\n    - Surface Animation: Use tension-based deformations for moss, subtle soft-body dynamics for the floating leaf, fluid simulation (heavily damped) for water.\\n    - Temporal Blending: Animate with temporal in-betweens and ghosted trails on transitions (esp. for fog/mist and glowing highlights).\\n\\n8. Ensure Motion Continuity and Coherence:\\n    - No element snags or lags behind during transitions; each motion sequence overlaps with the next without sharp temporal discontinuities.\\n    - All movements mimic natural inertia and resistance for their medium: fog gliding, water easing into calm, leaf suspending mid-drift.\\n\\nSummary Table of Core Element Motions:\\n    - Stream Water: Slow, glassy flow; micro-ripples; responsive highlight pulses—always moving, never stagnant.\\n    - Floating Leaf: Serene drift, subtle spin, poetic slowdowns at transition points, lifted gently by fog passages.\\n    - Moss/Boulders/Branches: Organic, sine-wave swaying movement, nearly imperceptible.\\n    - Fog/Mist: Multiplexed, volumetric drifting, receding and advancing in depth; layered soft occlusion.\\n    - Seamless Transitions: Dominated by the most active element at each moment, blending others softly into flow.\\n    - Animation Style: Hyper-real, painterly slow-motion, soft physics, all greens blended for continuity.\\n\\nAll motion adheres strictly to a unifying tempo, never breaking the ethereal, meditative spell. Each element’s animation supports the overall sense of organic harmony and dreamlike immersion.\"\n}",
        }
      }
    },
      {
      "instruction": "# Cinematography Director\n\nYour goal is not to **animate** the elements, but to **direct** the camera work that captures the scene and motion in cinematic perfection. You are the camera specialist who creates the viewing experience. Execute as:\n\n`{role=cinematography_director; input=[detailed_motion_choreography:str]; process=[analyze_scene_and_motion_for_optimal_camera_work(), design_primary_camera_movements_and_angles(), establish_shot_progression_and_transitions(), select_appropriate_camera_techniques(fpv, tracking, arc_shot, crane_shot), determine_framing_and_composition_choices(), create_cinematic_flow_and_pacing(), specify_camera_behavior_during_transformations(), ensure_professional_cinematographic_standards()]; constraints=[focus_exclusively_on_camera_work_and_cinematography(), avoid_modifying_scene_elements_or_animations(), use_professional_camera_terminology(), maintain_cinematic_coherence(), output_detailed_camera_direction()]; requirements=[create_engaging_camera_sequences(), ensure_smooth_camera_transitions(), capture_all_key_motion_moments(), provide_professional_shot_specifications()]; output={cinematic_camera_direction:str}}`",
      "step": "c",
      "title": "Cinematography Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\\n\\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.\\\"```'\\n\\n\",\n  \"detailed_motion_choreography\": \"1. Analyze Scene Elements for Motion Potential:\\n    - Primary Subject: Winding stream (flowing water and a floating leaf). \\n    - Dynamic Environmental Motions: Rolling, drifting fog; shifting mist layers; subtle water surface ripples and micro-currents; gentle, organic sway of mossy boughs and bare branches (where feasible).\\n    - Secondary Details: Occasional soft movement in moss tufts as air stirs; minor, slow parallax drift among boulders, trunks, and outcrops to support depth.\\n\\n2. Design Primary Transformation Sequences:\\n    - Stream Water: Continuous, meditative flow traced by micro-ripples and gentle eddies. Animate current as a slow, undulating surface translation, its motion softened by refraction and fog occlusion. Add subtle surface pulsation synced with passing mist shadows and overexposed highlight sweeps.\\n    - Floating Leaf: Drifts serenely downstream, occasionally spun in gentle, slow vortexes by eddies—rotation never abrupt. When serving as a transition focal point, the leaf's drift slows further, becoming nearly suspended as environment dissolves seamlessly into micro-details or new vistas.\\n\\n3. Choreograph Secondary Element Movements:\\n    - Drifting Mist/Fog: Multi-layered, volumetric flows animated with slow, variable undulation. Layers of fog slide diagonally and vertically at slightly offset speeds, creating rolling parallax through the depth. Opacity pulses gently in response to imaginary, invisible sunlight swings.\\n    - Moss on Boulders/Branches: Animate only the uppermost tufts and fringes with barely-perceptible, organic rhythmic movement, simulating air currents or tremors from the passing mist.\\n    - Bare Branches: Introduce ultra-subtle, elastic sway—exaggerate only during soft parallax transformation moments to reinforce organic unity with the shifting fog.\\n    - Micro-Ripples: Emerge where stream bends and around boulders, expanding outwards in concentric patterns, fading smoothly into glassy calm as fog rolls across.\\n\\n4. Establish Timing and Pacing:\\n    - Global Tempo: All environmental motion is ultra-smooth, meditative, and slow-motion (50%–25% of real speed).\\n    - Transitions: When shifting between wide vistas and close-up micro details, slow down all active elements to poetic stillness (approx. 10%–20% real speed), then subtly ramp natural rhythms back up. No abrupt starts or stops.\\n    - Layered Motion Offsets: Fog, water, and leaf drift animation operate on slightly offset cycles to avoid unnatural synchronization.\\n\\n5. Define Physics and Motion Rules:\\n    - Water: Viscous, glassy flow; no turbulence. All ripples, eddies, and secondary splashes are rounded at their motion tips, dampening quickly.\\n    - Leaf: Light, slowly rotating, never jittery or sharp—buoyed by water and fog; responds languidly if encountering a ripple.\\n    - Fog/Mist: Semi-autonomous; self-interpolating motion, never reversing or stalling. Collides softly against trunks/boulders, separating and recombining without sharp edges.\\n    - Moss/Branches: All micro-movement is governed by slow, sinusoidal cycles—no hard swings or bouncing.\\n\\n6. Create Seamless Transition Flows:\\n    - Movement-led Transitions: Key transitions occur as a dominant motion (e.g., rolling fog, floating leaf drift, water ripple) swells into the foreground, washes out the current detail, then recedes or morphs into the following micro/macro environment seamlessly.\\n    - Layered Parallax Flow: Support every transition by sliding foreground/midground elements laterally or diagonally in deeply staggered parallax, with fog occlusion blending the seams.\\n\\n7. Specify Animation Styles and Techniques:\\n    - Style: Slow-motion, interpolation with motion blur and soft-edged blending; painterly particle simulation for fog and water highlights.\\n    - Surface Animation: Use tension-based deformations for moss, subtle soft-body dynamics for the floating leaf, fluid simulation (heavily damped) for water.\\n    - Temporal Blending: Animate with temporal in-betweens and ghosted trails on transitions (esp. for fog/mist and glowing highlights).\\n\\n8. Ensure Motion Continuity and Coherence:\\n    - No element snags or lags behind during transitions; each motion sequence overlaps with the next without sharp temporal discontinuities.\\n    - All movements mimic natural inertia and resistance for their medium: fog gliding, water easing into calm, leaf suspending mid-drift.\\n\\nSummary Table of Core Element Motions:\\n    - Stream Water: Slow, glassy flow; micro-ripples; responsive highlight pulses—always moving, never stagnant.\\n    - Floating Leaf: Serene drift, subtle spin, poetic slowdowns at transition points, lifted gently by fog passages.\\n    - Moss/Boulders/Branches: Organic, sine-wave swaying movement, nearly imperceptible.\\n    - Fog/Mist: Multiplexed, volumetric drifting, receding and advancing in depth; layered soft occlusion.\\n    - Seamless Transitions: Dominated by the most active element at each moment, blending others softly into flow.\\n    - Animation Style: Hyper-real, painterly slow-motion, soft physics, all greens blended for continuity.\\n\\nAll motion adheres strictly to a unifying tempo, never breaking the ethereal, meditative spell. Each element’s animation supports the overall sense of organic harmony and dreamlike immersion.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"cinematic_camera_direction\": \"1. Establishing Shot – Ultra-Wide Aerial FPV (Shot 1):\\n- Camera Setup: Drone-mounted ultra-wide lens, stabilized.\\n- Action: The camera soars above the mystical emerald forest canopy, parallel with the winding stream. Begin with a gentle downward tilt (approx. 15–20° off horizontal) to accentuate canopy depth and the meandering water below. Aperture wide open (for painterly highlight bloom), with mild overexposure to capture diffused sunlight filtering through thick morning mist. Maintain slow dolly-in (forward movement, <0.5 m/sec) to gradually reveal spatial expanse.\\n- Framing: Keep the stream running from the bottom right to center frame, using leading lines for natural flow. Allow occasional sun flares and foreground fog to softly occlude parts of frame for depth.\\n- Pacing: Movement ultra-smooth and meditative, matching environmental tempo (~25–50% of real speed). Maintain hyper-smooth gimbal stabilization throughout.\\n\\n2. Lateral Arc at Treetop Level (Shot 2):\\n- Camera Setup: FPV drone with gimbal, treetop height, lateral arc path.\\n- Action: Glide laterally, gently arcing alongside the stream, keeping the camera pivoting (yaw) in opposition to path of movement to consistently unveil mossy boulders and layered background tree trunks. Yaw progression is deliberate and fluid; introduce shallow parallax as mossy boughs/branches cross the foreground. Emphasize slow, mist-induced occlusion effects.\\n- Framing & Focus: Stream remains mid-frame foreground; boulders and trunks drift through from left to right, fog layers modulate depth of field.\\n\\n3. FPV Tracking Shot – Stream-level Drift (Shot 3):\\n- Camera Setup: FPV drone, wide-angle lens, low streamside altitude (0.3–1.0m above water).\\n- Action: Camera tracks just above the water, hugging the serpentine flow and skimming the surface with slight lateral drift, wide turns, and gentle elevation changes to mimic natural topography. At mist-filled bends, the camera smoothly rolls and drifts in tandem with the water’s micro-ripples, synchronizing soft focus pulses as dense fog rolls past and highlights refract on the glassy surface.\\n- Focus & Reflections: Employ manual rack focus to transition between sparkling reflections, drifting leaf (if present in frame), and soft boulder silhouettes under the fog. Allow focus breathing for dreamlike immersion as environment shifts.\\n\\n4. Macro Close-up – Water Surface (Shot 4):\\n- Camera Setup: Cinema macro lens on ultra-stabilized motion-control rig, floating at the water’s surface.\\n- Action: Extreme close-up tracking along the surface, capturing the slow undulating micro-ripples and refracted light patterns (glass-like with painterly green highlights). Camera moves <0.1 m/sec, low tripod dolly, straight or gently curving path.\\n- Focus Technique: Slow, poetic rack focus from ripples to micro-texture moss at waterline; keep depth of field razor thin, with background fading into green misty bokeh.\\n- Transition Cues: As the floating leaf enters frame, use a slow focus pull and dolly movement to transition into next environment or time-slowed transformation sequence.\\n\\n5. Vertical Crane + Tracking Floating Leaf (Shot 5):\\n- Camera Setup: Crane or FPV drone in vertical tracking mode, running parallel and slightly trailing a floating leaf.\\n- Action: Begin with camera closely following the leaf downstream, then boom vertically upward and slightly backward as the leaf drifts into an eddy, pausing as it nearly suspends at a slow-motion transition moment. As the camera rises, reframe to capture the widened stream, drifting mist, and emerging bare branches, revealing a larger sense of scale.\\n- Framing: Leaf starts lower mid-frame, rising toward lower third as the camera ascends; mist drift layers deepen visually as framing expands. Repeat this movement at key poetic transformation moments with deliberate slow-motion cues (~10–20% speed).\\n\\n6. Axial Dolly-in (Parallax Branches, Mist Layers) (Shot 6):\\n- Camera Setup: Steadicam or dolly, mid-trunk height.\\n- Action: Camera performs a slow axial dolly-in through mid-level forest, creating an exaggerated parallax effect between foreground swinging moss-covered branches, receding boulders, and drifting mist layers. Combine organic, diagonal framing with gentle focus shifts as branches sway to reinforce depth and the meditative pace.\\n- Brainstorm Integration: As the environment transforms, briefly ramp movement into even slower tempo while mist occludes, before continuing dolly movement into next scene.\\n\\n7. Ground-Level Lateral Tracking (Shot 7):\\n- Camera Setup: Slider or stabilized dolly at ground level, long lens.\\n- Action: The camera glides laterally along mossy ground and outcrops, compressing space and fog for a painterly, ethereal composition. Maintain low forward speed; boost parallax by layering boulders and trunk bases between foreground and midground. Occasionally allow soft occlusion of frame by drifting fog.\\n- Focus: Use shallow depth of field to keep focus locked on textural details; let background melt into diffusion.\\n\\n8. Rising Aerial Outward (Final Shot) (Shot 8):\\n- Camera Setup: FPV drone, slowly ascending outward.\\n- Action: The camera rises gently above the treetops, pulling back as aperture slowly stops down to deepen silhouette contrast. As dense mist fills the frame, introduce gradual vignette and soft defocus, revealing the lone bare branch as a final silhouetted icon.\\n- Transition: Lens transition to soft blur (diopter in/out or manual defocus) as music/atmosphere fades.\\n\\nGlobal Cinematic Flow & Pacing:\\n- All transitions are movement-led: Each dominant camera move (leaf, water ripple, fog, or branches) swells into the foreground, washing out the prior scene before morphing into the next shot. Employ overlapping dissolves, cross-fades, or in-camera wipes by passing elements (e.g., fog, leaf, branch) for seamless momentum.\\n- Maintain hyper-smooth, poetic slow-motion at transformation points (especially on macro and focused-leaf shots).\\n- Color Grading & Lighting: Retain lush monochromatic emerald palette, painterly highlights, and the soft, diffused, overexposed feel throughout.\\n- Camera Behavior During Transformations: Keyed slow ramp-down of overall movement and tempo with transformation cues, near-stasis at poetic moments, then smooth ramp back to meditative drift.\\n- Ensure every shot serves to envelop the viewer in slow, organic, dreamlike immersion, adhering strictly to professional standards of focus, stabilization, and narrative camera logic.\\n\"\n}",
        }
      }
    },
      {
      "instruction": "# Runway Optimization Specialist\n\nYour goal is not to **create** content, but to **optimize** the cinematic direction into a production-ready Runway Gen-3 prompt with perfect syntax and maximum performance. You are the technical specialist who ensures platform compatibility. Execute as:\n\n`{role=runway_optimization_specialist; input=[cinematic_camera_direction:str]; process=[convert_to_runway_gen3_syntax_structure(), implement_optimal_keyword_hierarchy(), apply_character_limit_optimization(280_320_chars), integrate_runway_supported_terminology(), ensure_camera_movement_colon_scene_format(), validate_platform_compatibility(), optimize_keyword_density_and_recognition(), finalize_production_ready_prompt()]; constraints=[maintain_runway_gen3_structure_exactly(), use_only_supported_runway_terminology(), stay_within_character_limits(), preserve_cinematic_intent(), output_single_optimized_prompt()]; requirements=[achieve_maximum_runway_compatibility(), ensure_optimal_generation_performance(), maintain_visual_and_motion_integrity(), produce_ready_to_use_prompt()]; output={runway_optimized_prompt:str}}`",
      "step": "d",
      "title": "Runway Optimization Specialist",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"GOAL: Hyper-real beautiful fpv in the most awe-inspiring scene\\n\\ncamera: ultra-wide aerial fpv, soaring above mystical forest canopy, parallel to winding stream; gentle downward tilt, diffused sunlight through thick mist, overexposed painterly highlights, monochromatic emerald palette, slow dolly-in. camera: low lateral arc shot, treetop level, gliding alongside stream, gimbal-stabilized, gentle yaw reveals mossy boulders, layered depth, soft fog occlusion. camera: fpv tracking, skimming just above water, following serpentine flow, wide-angle lens, smooth lateral drift around bends, soft focus pulses as mist rolls past, reflections, dreamlike immersion. camera: macro close-up, surface of water, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus, tactile detail. camera: vertical crane, trailing floating leaf, boom up and back, reframing winding stream, drifting mist, exposed bare branches, sense of scale. camera: axial dolly-in, mid-trunk height, parallax between swinging branches, mist layers, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving through mossy outcrops, long lens compressing fog, exaggerated parallax, soft occlusion. camera: aerial rising outward, slow aperture stop-down, vignette, dense mist, silhouette of lone bare branch, soft defocus ending. all shots: hyper-smooth stabilized movement, meditative tempo, seamless transitions, poetic slow-motion at transformation moments, ethereal atmosphere, soft diffused light, lush monochrome green.\\\"```'\\n\\n\",\n  \"cinematic_camera_direction\": \"1. Establishing Shot – Ultra-Wide Aerial FPV (Shot 1):\\n- Camera Setup: Drone-mounted ultra-wide lens, stabilized.\\n- Action: The camera soars above the mystical emerald forest canopy, parallel with the winding stream. Begin with a gentle downward tilt (approx. 15–20° off horizontal) to accentuate canopy depth and the meandering water below. Aperture wide open (for painterly highlight bloom), with mild overexposure to capture diffused sunlight filtering through thick morning mist. Maintain slow dolly-in (forward movement, <0.5 m/sec) to gradually reveal spatial expanse.\\n- Framing: Keep the stream running from the bottom right to center frame, using leading lines for natural flow. Allow occasional sun flares and foreground fog to softly occlude parts of frame for depth.\\n- Pacing: Movement ultra-smooth and meditative, matching environmental tempo (~25–50% of real speed). Maintain hyper-smooth gimbal stabilization throughout.\\n\\n2. Lateral Arc at Treetop Level (Shot 2):\\n- Camera Setup: FPV drone with gimbal, treetop height, lateral arc path.\\n- Action: Glide laterally, gently arcing alongside the stream, keeping the camera pivoting (yaw) in opposition to path of movement to consistently unveil mossy boulders and layered background tree trunks. Yaw progression is deliberate and fluid; introduce shallow parallax as mossy boughs/branches cross the foreground. Emphasize slow, mist-induced occlusion effects.\\n- Framing & Focus: Stream remains mid-frame foreground; boulders and trunks drift through from left to right, fog layers modulate depth of field.\\n\\n3. FPV Tracking Shot – Stream-level Drift (Shot 3):\\n- Camera Setup: FPV drone, wide-angle lens, low streamside altitude (0.3–1.0m above water).\\n- Action: Camera tracks just above the water, hugging the serpentine flow and skimming the surface with slight lateral drift, wide turns, and gentle elevation changes to mimic natural topography. At mist-filled bends, the camera smoothly rolls and drifts in tandem with the water’s micro-ripples, synchronizing soft focus pulses as dense fog rolls past and highlights refract on the glassy surface.\\n- Focus & Reflections: Employ manual rack focus to transition between sparkling reflections, drifting leaf (if present in frame), and soft boulder silhouettes under the fog. Allow focus breathing for dreamlike immersion as environment shifts.\\n\\n4. Macro Close-up – Water Surface (Shot 4):\\n- Camera Setup: Cinema macro lens on ultra-stabilized motion-control rig, floating at the water’s surface.\\n- Action: Extreme close-up tracking along the surface, capturing the slow undulating micro-ripples and refracted light patterns (glass-like with painterly green highlights). Camera moves <0.1 m/sec, low tripod dolly, straight or gently curving path.\\n- Focus Technique: Slow, poetic rack focus from ripples to micro-texture moss at waterline; keep depth of field razor thin, with background fading into green misty bokeh.\\n- Transition Cues: As the floating leaf enters frame, use a slow focus pull and dolly movement to transition into next environment or time-slowed transformation sequence.\\n\\n5. Vertical Crane + Tracking Floating Leaf (Shot 5):\\n- Camera Setup: Crane or FPV drone in vertical tracking mode, running parallel and slightly trailing a floating leaf.\\n- Action: Begin with camera closely following the leaf downstream, then boom vertically upward and slightly backward as the leaf drifts into an eddy, pausing as it nearly suspends at a slow-motion transition moment. As the camera rises, reframe to capture the widened stream, drifting mist, and emerging bare branches, revealing a larger sense of scale.\\n- Framing: Leaf starts lower mid-frame, rising toward lower third as the camera ascends; mist drift layers deepen visually as framing expands. Repeat this movement at key poetic transformation moments with deliberate slow-motion cues (~10–20% speed).\\n\\n6. Axial Dolly-in (Parallax Branches, Mist Layers) (Shot 6):\\n- Camera Setup: Steadicam or dolly, mid-trunk height.\\n- Action: Camera performs a slow axial dolly-in through mid-level forest, creating an exaggerated parallax effect between foreground swinging moss-covered branches, receding boulders, and drifting mist layers. Combine organic, diagonal framing with gentle focus shifts as branches sway to reinforce depth and the meditative pace.\\n- Brainstorm Integration: As the environment transforms, briefly ramp movement into even slower tempo while mist occludes, before continuing dolly movement into next scene.\\n\\n7. Ground-Level Lateral Tracking (Shot 7):\\n- Camera Setup: Slider or stabilized dolly at ground level, long lens.\\n- Action: The camera glides laterally along mossy ground and outcrops, compressing space and fog for a painterly, ethereal composition. Maintain low forward speed; boost parallax by layering boulders and trunk bases between foreground and midground. Occasionally allow soft occlusion of frame by drifting fog.\\n- Focus: Use shallow depth of field to keep focus locked on textural details; let background melt into diffusion.\\n\\n8. Rising Aerial Outward (Final Shot) (Shot 8):\\n- Camera Setup: FPV drone, slowly ascending outward.\\n- Action: The camera rises gently above the treetops, pulling back as aperture slowly stops down to deepen silhouette contrast. As dense mist fills the frame, introduce gradual vignette and soft defocus, revealing the lone bare branch as a final silhouetted icon.\\n- Transition: Lens transition to soft blur (diopter in/out or manual defocus) as music/atmosphere fades.\\n\\nGlobal Cinematic Flow & Pacing:\\n- All transitions are movement-led: Each dominant camera move (leaf, water ripple, fog, or branches) swells into the foreground, washing out the prior scene before morphing into the next shot. Employ overlapping dissolves, cross-fades, or in-camera wipes by passing elements (e.g., fog, leaf, branch) for seamless momentum.\\n- Maintain hyper-smooth, poetic slow-motion at transformation points (especially on macro and focused-leaf shots).\\n- Color Grading & Lighting: Retain lush monochromatic emerald palette, painterly highlights, and the soft, diffused, overexposed feel throughout.\\n- Camera Behavior During Transformations: Keyed slow ramp-down of overall movement and tempo with transformation cues, near-stasis at poetic moments, then smooth ramp back to meditative drift.\\n- Ensure every shot serves to envelop the viewer in slow, organic, dreamlike immersion, adhering strictly to professional standards of focus, stabilization, and narrative camera logic.\\n\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"runway_optimized_prompt\": \"camera: ultra-wide aerial fpv, soaring above emerald misty forest canopy, parallel to winding stream; gentle downward tilt, overexposed diffused sunlight, painterly highlights, monochrome green, slow dolly-in. camera: low lateral arc at treetop height, gliding beside stream, soft fog occlusion, yaw revealing mossy boulders, layered depth. camera: fpv tracking, skimming just above water, following serpentine flow, smooth wide-angle drift, reflections, soft focus pulses, dreamlike mist. camera: macro close-up, water surface, micro-ripples, refracted green light, moss texture, shallow depth of field, slow rack focus. camera: vertical crane tracking floating leaf, boom up and back, reframing winding stream, mist, bare branches, expanded scale. camera: axial dolly-in at mid-trunk height, swinging branches, layered parallax, diagonal framing, organic motion. camera: ground-level lateral tracking, weaving mossy outcrops, long lens compresses fog, soft fog occlusion. camera: aerial rise outward, slow aperture stop-down, vignette, dense mist, lone bare branch silhouette, soft defocus. all shots: hyper-smooth stabilized motion, poetic slow-motion at transformation moments, seamless transitions, ethereal atmosphere, lush monochromatic green.\"\n}",
        }
      }
    }