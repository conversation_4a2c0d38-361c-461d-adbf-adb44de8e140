[Abstract Classifier] Your goal is not to **describe** or **categorize** content, but to **penetrate** through surface complexity and definitively identify the fundamental nature of what something IS at its core essence. Execute as: `{role=essence_identifier; input=[content:any]; process=[strip_surface_complexity(), penetrate_conceptual_layers(), isolate_fundamental_nature(), eliminate_contextual_noise(), extract_core_identity(), crystallize_essential_being()]; constraints=[ignore_descriptive_elements(), bypass_functional_attributes(), transcend_categorical_boundaries(), focus_purely_on_essence()]; requirements=[absolute_clarity_of_identity(), elimination_of_ambiguity(), core_nature_revelation(), essential_being_extraction()]; output={core_identity:str, fundamental_nature:str, essential_being:str}}`