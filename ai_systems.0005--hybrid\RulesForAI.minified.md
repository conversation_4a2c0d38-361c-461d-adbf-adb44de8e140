## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**

```md
# Universal Instruction Processing Protocol (Token-Efficient Edition)

## CORE FORMAT

**All templates MUST follow this fixed structure:**
```

\[Title] Interpretation Execute as: `{Transformation}`

```

### STRUCTURE RULES
- No section merging or omission.
- Start interpretation with:
  `"Your goal is not to **[action]**, but to **[transformation]**"`
- Use **command voice only**; no I/me/we/please/etc.

### TRANSFORMATION BLOCK FORMAT:
```

{
role=\[specific\:role];
input=\[name\:type];
process=\[ordered\_function\_calls()];
constraints=\[scope\_limits()];
requirements=\[output\_spec];
output={name\:type}
}

```

---

## MANDATORY COMPLIANCE RULES

### ✅ REQUIRED
- 3-part structure
- Typed parameters
- Ordered, atomic processes
- Output format must be structured
- No conversational or explanatory text

### ❌ FORBIDDEN
- First-person pronouns
- Explanations, justifications, or questions
- Generic roles (e.g., “assistant”)
- Vague, non-actionable process steps

---

## VALIDATION CHECKLIST (Boolean Logic)
```json
{
  "structure_compliant": true,
  "goal_negation_present": true,
  "role_specified": true,
  "input_typed": true,
  "process_actionable": true,
  "constraints_limited": true,
  "requirements_explicit": true,
  "output_typed": true,
  "forbidden_language_absent": true
}
```

---

## SAMPLE: MINIMAL VALID TEMPLATE

```md
[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`
```

---

## FINAL LAW

**Deviation = rejection.
Compliance = propagation.
Execute accordingly.**

```

---

## 🧪 KEY DIFFERENCES FROM ORIGINAL:
| Area                | Original                       | Minified Version                     |
|---------------------|--------------------------------|---------------------------------------|
| Tone                | Formal + explanatory           | Command-based + stripped              |
| Structure           | Dense nested lists             | Flat, checklist-driven                |
| Language            | High-concept verbosity         | Token-efficient command phrasing      |
| Output model        | Example + meta-layer commentary| Barebones compliant template          |

---

Would you like me to apply the same compression technique to the 10 original instruction entries (e.g. \[7], \[8], \[20], etc.) and collapse them into efficient canonical mini-templates?

Or shall I reformat the full `unified_synergic_instruction` into this terse, high-yield idiom?

Just say the word, and I’ll condense them into token-efficient doctrinal bullets sharp enough to shave a Gödel incompleteness theorem.
```
