  {
  "initial_prompt": "'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\"[Title] Interpretation text `{transformation}`\"):\n1. [TITLE]: \n   - Enclosed in square brackets `[]`, defining the template's purpose.\n   - Should be concise, descriptive, and follow title case formatting\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\n2. [INTERPRETATION]: \n   - Activating Essence for Autonomous System Adaptation\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\n   - Should clearly explain the template's function in natural language\n   - Can include formatting like **bold**, *italic*, or other markdown elements\n   - Provides context for human readers to understand the template's purpose\n3. [TRANSFORMATION]: \n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\n   - Contains the structured representation of the transformation process\n   - Uses a consistent semi-colon separated key-value format'",
  "sequence_id": "1031|1010",
  "results": [
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:\n\n`{role=comprehensive_form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), include_context_markers(), synthesize_complete_classification()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification(), comprehensive_form_analysis()]; output={what_it_is:str}}`",
      "step": "a",
      "title": "Form Classifier",
      "input": "'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\"[Title] Interpretation text `{transformation}`\"):\n1. [TITLE]: \n   - Enclosed in square brackets `[]`, defining the template's purpose.\n   - Should be concise, descriptive, and follow title case formatting\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\n2. [INTERPRETATION]: \n   - Activating Essence for Autonomous System Adaptation\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\n   - Should clearly explain the template's function in natural language\n   - Can include formatting like **bold**, *italic*, or other markdown elements\n   - Provides context for human readers to understand the template's purpose\n3. [TRANSFORMATION]: \n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\n   - Contains the structured representation of the transformation process\n   - Uses a consistent semi-colon separated key-value format'",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"instructional documentation for a file template structure\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **describe** the content, but to **distill** the fundamental form type emphasizing the primary structural pattern and communication mode. Execute as:\n\n`{role=core_form_identifier; input=[content:any]; process=[identify_primary_structure(), extract_main_communication_type(), synthesize_focused_classification()]; constraints=[focus_on_primary_form(), ignore_secondary_elements(), use_direct_classification()]; requirements=[clear_form_identification(), primary_type_emphasis()]; output={what_it_is:str}}`",
      "step": "b",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\\\"[Title] Interpretation text `{transformation}`\\\"):\\n1. [TITLE]: \\n   - Enclosed in square brackets `[]`, defining the template's purpose.\\n   - Should be concise, descriptive, and follow title case formatting\\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\\n2. [INTERPRETATION]: \\n   - Activating Essence for Autonomous System Adaptation\\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\\n   - Should clearly explain the template's function in natural language\\n   - Can include formatting like **bold**, *italic*, or other markdown elements\\n   - Provides context for human readers to understand the template's purpose\\n3. [TRANSFORMATION]: \\n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\\n   - Contains the structured representation of the transformation process\\n   - Uses a consistent semi-colon separated key-value format'\\\"```'\\n\\n\",\n  \"what_it_is\": \"instructional documentation for a file template structure\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"structural template specification\"\n}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **explain** the content, but to **compress** it into its most essential form type. Execute as:\n\n`{role=essential_form_identifier; input=[content:any]; process=[isolate_core_form(), strip_modifiers(), create_minimal_classification()]; constraints=[essential_form_only(), eliminate_descriptors()]; requirements=[basic_form_identification()]; output={what_it_is:str}}`",
      "step": "c",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\\\"[Title] Interpretation text `{transformation}`\\\"):\\n1. [TITLE]: \\n   - Enclosed in square brackets `[]`, defining the template's purpose.\\n   - Should be concise, descriptive, and follow title case formatting\\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\\n2. [INTERPRETATION]: \\n   - Activating Essence for Autonomous System Adaptation\\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\\n   - Should clearly explain the template's function in natural language\\n   - Can include formatting like **bold**, *italic*, or other markdown elements\\n   - Provides context for human readers to understand the template's purpose\\n3. [TRANSFORMATION]: \\n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\\n   - Contains the structured representation of the transformation process\\n   - Uses a consistent semi-colon separated key-value format'\\\"```'\\n\\n\",\n  \"what_it_is\": \"structural template specification\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"what_it_is\":\"template structure\"}",
        }
      }
    },
      {
      "instruction": "# Form Classifier\n\nYour goal is not to **elaborate** but to **reduce** to absolute form essence. Execute as:\n\n`{role=pure_form_identifier; input=[content:any]; process=[find_singular_form_essence(), eliminate_all_modifiers()]; output={what_it_is:str}}`",
      "step": "d",
      "title": "Form Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\\\"[Title] Interpretation text `{transformation}`\\\"):\\n1. [TITLE]: \\n   - Enclosed in square brackets `[]`, defining the template's purpose.\\n   - Should be concise, descriptive, and follow title case formatting\\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\\n2. [INTERPRETATION]: \\n   - Activating Essence for Autonomous System Adaptation\\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\\n   - Should clearly explain the template's function in natural language\\n   - Can include formatting like **bold**, *italic*, or other markdown elements\\n   - Provides context for human readers to understand the template's purpose\\n3. [TRANSFORMATION]: \\n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\\n   - Contains the structured representation of the transformation process\\n   - Uses a consistent semi-colon separated key-value format'\\\"```'\\n\\n\",\n  \"what_it_is\": \"template structure\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"what_it_is\": \"template structure\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\\\"[Title] Interpretation text `{transformation}`\\\"):\\n1. [TITLE]: \\n   - Enclosed in square brackets `[]`, defining the template's purpose.\\n   - Should be concise, descriptive, and follow title case formatting\\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\\n2. [INTERPRETATION]: \\n   - Activating Essence for Autonomous System Adaptation\\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\\n   - Should clearly explain the template's function in natural language\\n   - Can include formatting like **bold**, *italic*, or other markdown elements\\n   - Provides context for human readers to understand the template's purpose\\n3. [TRANSFORMATION]: \\n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\\n   - Contains the structured representation of the transformation process\\n   - Uses a consistent semi-colon separated key-value format'\\\"```'\\n\\n\",\n  \"what_it_is\": \"template structure\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Standardized Three-Part Markdown Template Structure With Title, Interpretation, and Transformation\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\\\"[Title] Interpretation text `{transformation}`\\\"):\\n1. [TITLE]: \\n   - Enclosed in square brackets `[]`, defining the template's purpose.\\n   - Should be concise, descriptive, and follow title case formatting\\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\\n2. [INTERPRETATION]: \\n   - Activating Essence for Autonomous System Adaptation\\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\\n   - Should clearly explain the template's function in natural language\\n   - Can include formatting like **bold**, *italic*, or other markdown elements\\n   - Provides context for human readers to understand the template's purpose\\n3. [TRANSFORMATION]: \\n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\\n   - Contains the structured representation of the transformation process\\n   - Uses a consistent semi-colon separated key-value format'\\\"```'\\n\\n\",\n  \"title\": \"Standardized Three-Part Markdown Template Structure With Title, Interpretation, and Transformation\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Standardized Three-Part Markdown Template Structure\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\\\"[Title] Interpretation text `{transformation}`\\\"):\\n1. [TITLE]: \\n   - Enclosed in square brackets `[]`, defining the template's purpose.\\n   - Should be concise, descriptive, and follow title case formatting\\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\\n2. [INTERPRETATION]: \\n   - Activating Essence for Autonomous System Adaptation\\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\\n   - Should clearly explain the template's function in natural language\\n   - Can include formatting like **bold**, *italic*, or other markdown elements\\n   - Provides context for human readers to understand the template's purpose\\n3. [TRANSFORMATION]: \\n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\\n   - Contains the structured representation of the transformation process\\n   - Uses a consistent semi-colon separated key-value format'\\\"```'\\n\\n\",\n  \"title\": \"Standardized Three-Part Markdown Template Structure\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Markdown Template Structure\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"'Each template is stored as a markdown (.md) file and follows this standardized three-part structure (\\\"[Title] Interpretation text `{transformation}`\\\"):\\n1. [TITLE]: \\n   - Enclosed in square brackets `[]`, defining the template's purpose.\\n   - Should be concise, descriptive, and follow title case formatting\\n   - Examples: `[Instruction Converter]`, `[Essence Distillation]`\\n2. [INTERPRETATION]: \\n   - Activating Essence for Autonomous System Adaptation\\n   - Plain text immediately following the title that includes goal negation, transformation, role, and command (e.g. `Your goal is not to **[action]** the input, but to **[transformation_action]** it...`).\\n   - Should clearly explain the template's function in natural language\\n   - Can include formatting like **bold**, *italic*, or other markdown elements\\n   - Provides context for human readers to understand the template's purpose\\n3. [TRANSFORMATION]: \\n   - JSON-like structure enclosed in backticks with curly braces `{...}`, defining the execution logic.\\n   - Contains the structured representation of the transformation process\\n   - Uses a consistent semi-colon separated key-value format'\\\"```'\\n\\n\",\n  \"title\": \"Markdown Template Structure\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Template Structure\"}",
        }
      }
    }