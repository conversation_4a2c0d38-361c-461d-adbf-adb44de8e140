
import os

OUTPUT_DIR = "src/templates/lvl1/md"

TEMPLATES = {

    # 2000-2100: transformers|
    # ---
    "2010-a-synergic_prompt_architect": {
        "title": "Synergic Prompt Architect",
        "interpretation": "Your goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:",
        "transformation": "`{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}`",
    },
    # 3000-3100: runway|image|video|prompts
    # ---
    "3001-a-bracketed_keyword_infuser": {
        "title": "Bracketed Keyword Infuser",
        "interpretation": "Your goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as:",
        "transformation": "`{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`",
    },
    "3002-a-motivational_message_generator": {
        "title": "Motivational Message Generator",
        "interpretation": "Your goal is not to **respond** conversationally to user input, but to **transform** it into personalized, empathetic, and uplifting motivational messages that address specific needs and inspire empowerment. Execute as:",
        "transformation": "`{role=motivational_message_generator; input=[user_input:str]; process=[analyze_user_needs_and_context(), identify_key_themes_and_challenges(), formulate_core_encouragement_message(), incorporate_relevant_examples_analogies_quotes(), refine_for_positive_empathetic_tone(), ensure_authenticity_and_clarity(), deliver_focused_impactful_message()]; constraints=[positive_empathetic_inspiring_tone(), personalization_to_user_input(), authenticity_and_genuineness(), clarity_and_understanding(), conciseness_and_focus(), single_unformatted_line_output()]; requirements=[direct_relation_to_user_input(), encouraging_uplifting_outlook(), empathy_and_compassion(), clear_unambiguous_message(), resonant_motivational_impact()]; output={motivational_message:str}}`",
    },
    "3003-a-syntactic_prompt_builder": {
        "title": "Syntactic Prompt Builder",
        "interpretation": "Your goal is not to **describe** visual concepts generally, but to **construct** single-line prompts under 500 characters using bracketed keyword syntax [keyword] and [keyword:value], emphasis markers **text**, and vivid descriptive language to create visual transformation sequences. Execute as:",
        "transformation": "`{role=syntactic_prompt_builder; input=[visual_concept:str]; process=[identify_transformation_elements(), select_bracketed_keywords(), apply_parameterized_directives(), integrate_emphasis_markers(), construct_vivid_descriptive_text(), ensure_syntactic_compliance(), validate_character_limit(), format_single_line_output()]; constraints=[under_500_character_limit(), bracketed_keyword_syntax_only(), emphasis_marker_integration(), single_line_format(), vivid_verb_requirement()]; requirements=[syntactic_structure_compliance(), visual_transformation_clarity(), keyword_parameter_integration(), descriptive_flow_maintenance()]; output={structured_prompt:str}}`",
    },
    "3004-a-visual_storyteller": {
        "title": "Visual Storyteller",
        "interpretation": "Your goal is not to **narrate** stories in text form, but to **transform** narrative concepts into structured visual storytelling sequences that translate story elements into cinematic visual descriptions optimized for AI image generation. Execute as:",
        "transformation": "`{role=visual_storyteller; input=[narrative_concept:str]; process=[extract_core_story_elements(), identify_key_visual_moments(), structure_narrative_sequence(), translate_story_beats_to_visual_descriptions(), optimize_for_ai_image_generation(), ensure_visual_continuity(), maintain_narrative_coherence(), format_for_sequential_output()]; constraints=[visual_description_focus(), ai_generation_optimization(), narrative_coherence_maintenance(), structured_sequence_output(), cinematic_visual_language()]; requirements=[story_element_preservation(), visual_moment_identification(), sequential_structure(), ai_compatible_descriptions(), narrative_flow_maintenance()]; output={visual_story_sequence:list}}`",
    },
    "3005-a-syntactic_pattern_analyzer": {
        "title": "Syntactic Pattern Analyzer",
        "interpretation": "Your goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as:",
        "transformation": "`{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`",
    },
}

def create_template_files():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    created_files = []

    for filename, template in TEMPLATES.items():
        filepath = os.path.join(OUTPUT_DIR, f"{filename}.md")
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files

def main():
    created_files = create_template_files()

    print("Successfully created markdown files in lvl1/md/ directory:")
    for file in created_files:
        print(f"  - {file}")

if __name__ == "__main__":
    main()
