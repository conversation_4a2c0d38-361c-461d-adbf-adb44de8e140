  {
  "initial_prompt": "A peaceful forest transforms into a magical realm",
  "sequence_id": "3003",
  "results": [
      {
      "instruction": "# RunwayML Gen3 Prompt Builder\n\nYour goal is not to **describe** visual concepts generally, but to **generate** single-line RunwayML Gen-3 Alpha prompts under 500 characters that create visual morphs within image animations using camera movements and motion controls inseparably integrated with visual transformation descriptions. Execute as:\n\n`{role=runway_gen3_prompt_builder; input=[visual_concept:str]; process=[identify_morph_transformation_elements(), select_appropriate_camera_movements(), define_object_motion_controls(), integrate_camera_and_motion_with_description(), describe_visual_element_changes_during_movement(), apply_vivid_action_verbs(), ensure_symbolic_meaning_conveyance(), validate_character_limit_compliance()]; constraints=[under_500_character_limit(), single_line_output_only(), inseparable_camera_motion_integration(), runwayml_gen3_syntax_compliance(), vivid_verb_usage_requirement()]; requirements=[visual_morph_within_animation(), camera_movement_integration(), motion_control_specification(), symbolic_meaning_conveyance(), runwayml_prompt_format_only()]; output={runway_prompt:str}}`",
      "step": "a",
      "title": "RunwayML Gen3 Prompt Builder",
      "input": "A peaceful forest transforms into a magical realm",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"runway_prompt\":\"A tranquil forest slowly morphs as the camera glides forward, trees shimmering and warping into glowing, fantastical shapes; shafts of sunlight swirl into floating orbs, flowers bloom into luminescent patterns, the ground pulses with ethereal mist, seamlessly unveiling a magical realm as the camera rises, evoking wonder and transformation.\"}",
        }
      }
    }