import os

class TemplateGeneratorBase:
    OUTPUT_DIR = "src/templates/lvl1/md"
    
    @classmethod
    def create_template_files(cls):
        if not hasattr(cls, 'TEMPLATES'):
            raise NotImplementedError("TEMPLATES must be defined in subclass")
            
        os.makedirs(cls.OUTPUT_DIR, exist_ok=True)
        created_files = []
        
        for filename, template in cls.TEMPLATES.items():
            filepath = os.path.join(cls.OUTPUT_DIR, f"{filename}.md")
            content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"
            
            with open(filepath, "w", encoding="utf-8") as f:
                f.write(content)
                
            created_files.append(f"{filename}.md")
        
        return created_files
    
    @classmethod
    def main(cls):
        created_files = cls.create_template_files()
        
        print("Successfully created markdown files in lvl1/md/ directory:")
        for file in created_files:
            print(f"  - {file}")
