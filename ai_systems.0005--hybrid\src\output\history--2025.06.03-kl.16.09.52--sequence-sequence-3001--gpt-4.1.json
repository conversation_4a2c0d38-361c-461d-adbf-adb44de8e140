  {
  "initial_prompt": "A serene mountain lake reflects the aurora borealis above, with gentle ripples spreading across the water surface as the northern lights dance in the sky.",
  "sequence_id": "3001",
  "results": [
      {
      "instruction": "# Camera Movement Infuser\n\nYour goal is not to **analyze** scene descriptions, but to **enhance** them by seamlessly integrating RunwayML-compatible camera movements and transitions while explicitly preserving shot structure and outputting structured two-shot sequences. Execute as:\n\n`{role=camera_movement_infuser; input=[scene_description:str]; process=[detect_transition_keywords(), separate_shot_a_and_shot_b(), identify_camera_movement_opportunities(), select_runwayml_compatible_keywords(), integrate_bracketed_keywords_into_descriptions(), maintain_shot_separation(), preserve_transition_structure(), validate_runwayml_compatibility()]; constraints=[preserve_user_provided_keywords(), use_only_official_runwayml_keywords(), maintain_clear_shot_delineation(), output_single_unformatted_line(), maximum_response_length_limit()]; requirements=[structured_two_shot_output(), enhanced_visual_narrative(), preservation_of_context(), runwayml_compatibility()]; output={enhanced_scene_description:str}}`",
      "step": "a",
      "title": "Camera Movement Infuser",
      "input": "A serene mountain lake reflects the aurora borealis above, with gentle ripples spreading across the water surface as the northern lights dance in the sky.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"enhanced_scene_description\":\"Shot A: A serene mountain lake reflects the aurora borealis above, with gentle ripples spreading across the water surface as the northern lights dance in the sky. [static, wide shot, slow pan upwards] Transition: [cross dissolve] Shot B: The camera [dolly in] towards the shimmering water, capturing the reflection of the swirling auroras as the gentle ripples softly distort the colors on the surface. [tracking shot]\"}",
        }
      }
    }