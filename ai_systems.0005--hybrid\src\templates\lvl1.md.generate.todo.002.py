
import os

OUTPUT_DIR = "src/templates/lvl1/md"

TEMPLATES = {
    "0100-a-instruction_generator": {
        "title": "instruction_generator",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",
        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
    },

}


"""
# =======================================================
# TODO
# =======================================================
├── outputs_json
│   ├── ContextualRelationalOptimizer.xml
│   ├── MetaPromptRefiner.xml
│   ├── PromptOptimizerExpert.xml
│   ├── PromptOptimizerExpertJson.json
│   ├── PromptOptimizerExpertPy.py
│   ├── PromptOptimizerExpertPy.py.bak1
│   ├── PromptOptimizerExpertTxt.py
│   ├── RelationFirstMetaSynthesizer.xml
│   └── SemanticSentenceExploder.xml
├── outputs_singleline
│   ├── amplifiers
│   │   ├── CodeCommentCondenser.xml
│   │   ├── EmphasisEnhancer.xml
│   │   ├── EmphasisEnhancer_a.xml
│   │   ├── EmphasisEnhancer_b.xml
│   │   ├── EmphasisEnhancer_c.xml
│   │   ├── ImpactRefinement.xml
│   │   ├── IntensityEnhancer.xml
│   │   ├── IntensityEnhancerNotes.xml
│   │   ├── IntensityEnhancer_a.xml
│   │   ├── IntensityEnhancer_b.xml
│   │   ├── IntensityEnhancer_c.xml
│   │   └── UltimateRefinement.xml
│   ├── builders
│   │   ├── BrilliantCameraShotTransformer.xml
│   │   ├── CameraMovementExtractor.xml
│   │   ├── CameraMovementInfuser.xml
│   │   ├── CinematicSceneDescriptor.xml
│   │   ├── CinematicSceneDescriptor_a.xml
│   │   ├── CinematicSceneDescriptor_b.xml
│   │   ├── CinematicSceneDescriptor_c.xml
│   │   ├── CinematicSceneDescriptor_d.xml
│   │   ├── EmotionalResonanceInfuser.xml
│   │   ├── PrehistoricSceneGenerator.xml
│   │   ├── RunwayPromptBuilder.xml
│   │   ├── RunwayPromptBuilder_d.xml
│   │   ├── RunwayPromptBuilder_e.xml
│   │   ├── SceneDescriptor.xml
│   │   ├── SceneDescriptor_a.xml
│   │   ├── SceneDescriptor_b.xml
│   │   ├── SceneDescriptor_c.xml
│   │   ├── SceneDescriptor_d.xml
│   │   ├── ScenicCompositionExpert.xml
│   │   ├── SpectacleInfusionAgent.xml
│   │   ├── SpectacleInfusionAgent_a.xml
│   │   ├── UnderwaterDayNightSceneGenerator.xml
│   │   └── VisualStoryteller.xml
│   ├── chains
│   │   ├── AbstractionTransformer
│   │   │   ├── AnimationRefiner.xml
│   │   │   ├── MathematicalDetailEnhancer.xml
│   │   │   ├── MathematicalVisualizer.xml
│   │   │   ├── MathematicalVisualizer_a.xml
│   │   │   ├── SceneContextualizer.xml
│   │   │   ├── SemanticSimplifier.xml
│   │   │   ├── StyleAndMoodInfuser.xml
│   │   │   ├── VisualClarityOptimizer.xml
│   │   │   └── VisualMetaphorGenerator.xml
│   │   ├── CognitiveMapper
│   │   │   ├── CognitiveMapper.md
│   │   │   ├── CognitiveStyleInfuser.xml
│   │   │   ├── HierarchicalOrganizer.xml
│   │   │   ├── KnowledgeContextualizer.xml
│   │   │   ├── KnowledgeExtractor.xml
│   │   │   ├── SemanticLinker.xml
│   │   │   └── VisualPathwaysGenerator.xml
│   │   ├── EmotionallyDrivenSceneBuilder
│   │   │   ├── AbstractConceptEnhancer.xml
│   │   │   ├── DramaticElementInfuser.xml
│   │   │   ├── EmotionallyDrivenSceneBuilder.md
│   │   │   ├── EmotionallyDrivenSceneBuilder.xml
│   │   │   ├── NostalgicMeadowScene.xml
│   │   │   └── RunwayPromptBuilder_g.xml
│   │   ├── InformationDistiller
│   │   │   ├── ContextualRephraser.xml
│   │   │   ├── CoreDataExtractor.xml
│   │   │   ├── DataStructureOptimizer.xml
│   │   │   ├── DataStructureOptimizerJson.json
│   │   │   ├── RedundancyEliminator.xml
│   │   │   └── ValuePrioritizer.xml
│   │   ├── MusicVisualize
│   │   │   ├── HarmonicDetailer.xml
│   │   │   ├── MusicalMetaphorGenerator.xml
│   │   │   ├── MusicalStructureAnalyzer.xml
│   │   │   ├── RhythmicAnimator.xml
│   │   │   ├── RhythmicAnimator_a.xml
│   │   │   └── VisualHarmonyOptimizer.xml
│   │   ├── RealityFabricator
│   │   │   ├── DimensionGenerator.xml
│   │   │   ├── DynamicSimulator.xml
│   │   │   ├── PerceptionShaper.xml
│   │   │   ├── PropertyManipulator.xml
│   │   │   ├── RealityMetaphorGenerator.xml
│   │   │   ├── RealityOutputGenerator.xml
│   │   │   └── RealityRuleExtractor.xml
│   │   ├── UniversalHarmony
│   │   │   ├── ScaleAndStructureGenerator.xml
│   │   │   └── UniversalRuleExtractor.xml
│   │   ├── SublimeTextContextTransformer.xml
│   │   ├── SublimeTextInstructionEnhancer.xml
│   │   ├── SublimeTextInstructionGenerator.xml
│   │   └── chains.md
│   ├── characters
│   │   ├── ArtSnobCritic.xml
│   │   ├── ArtSnobCritic_a.xml
│   │   ├── PersonaImpersonation.xml
│   │   └── PhilosophicalMusings.xml
│   ├── clarifiers
│   │   ├── AdjustedToPerfection.xml
│   │   ├── AdjustedToPerfection_a.xml
│   │   ├── AdjustedToPerfection_b.xml
│   │   ├── AdjustedToPerfection_c.xml
│   │   ├── ClarityEnhancer.xml
│   │   ├── ClarityEnhancer_a.xml
│   │   ├── PromptEnhancer.xml
│   │   ├── PromptEnhancer2.xml
│   │   ├── RephraseAsQuestion.xml
│   │   └── ResonanceAndRelatability.xml
│   ├── evaluators
│   │   └── BiasEvaluator.xml
│   ├── formatters
│   │   ├── ExpandSingleLines.xml
│   │   ├── SingleLineFormatterForced.xml
│   │   ├── SingleLineFormatterForced_a.xml
│   │   ├── SingleLineFormatterSmartBreaks.xml
│   │   └── StripFormatting.xml
│   ├── generators
│   │   ├── CritiqueGenerator.xml
│   │   ├── ExampleGenerator.xml
│   │   ├── IdeaExpander.xml
│   │   ├── IdeaGenerator.xml
│   │   └── MotivationalMuse.xml
│   ├── identifiers
│   │   ├── KeyFactorIdentifier.xml
│   │   └── KeyFactorMaximizer.xml
│   ├── meta
│   │   ├── RedefineFromMetaPerspective_a.xml
│   │   └── RedefineFromMetaPerspective_b.xml
│   ├── optimizers
│   │   ├── KeyFactorOptimizer.xml
│   │   └── StrategicValueOptimizer.xml
│   ├── personas
│   │   └── keenly_unaware
│   │       ├── ConstrainedPersonaInterpolation.xml
│   │       ├── GuidedPersonaIntegration.xml
│   │       ├── ImpactfulPersonaRestatement.xml
│   │       ├── PersonaImpersonation2.xml
│   │       ├── PersonaImpersonation3.xml
│   │       ├── PersonaImpersonation4.xml
│   │       ├── PersonaInterpolation.xml
│   │       ├── PersonaInterpolation_b.xml
│   │       └── PersonaIntroduction.xml
│   ├── reducers
│   │   ├── ComplexityReducer.xml
│   │   ├── ExtractMeaning.xml
│   │   ├── IntensityReducer.xml
│   │   ├── InterpretMeaning.xml
│   │   ├── StrategicInsightsHighlighter.xml
│   │   ├── TitleExtractor.xml
│   │   └── ValuableConversationSummarizer_a.xml
│   ├── transformers
│   │   ├── AbstractContextualTransformer.xml
│   │   ├── AdaptiveEditor.xml
│   │   ├── ColoringAgent.xml
│   │   ├── GoalSetter.xml
│   │   ├── GoalSetter_a.xml
│   │   ├── GoalSetter_b.xml
│   │   ├── GrammarCorrector.xml
│   │   ├── ImpactfulRestatement.xml
│   │   ├── PerformanceEnhancer.xml
│   │   ├── PoeticRefiner.xml
│   │   ├── PoeticRefiner_a.xml
│   │   ├── PoeticRefiner_b.xml
│   │   ├── PoteticPerfection.xml
│   │   ├── PoteticPerfection_a1.xml
│   │   ├── PoteticPerfection_a2.xml
│   │   ├── PoteticPerfection_b1.xml
│   │   ├── PoteticPerfection_b2.xml
│   │   ├── PoteticPerfection_b3.xml
│   │   ├── PoteticPerfection_b4.xml
│   │   ├── RephraseAsSentence.xml
│   │   └── SubtlePoeticRefiner.xml
│   ├── translators
│   │   ├── EnglishToNorwegianTranslator.xml
│   │   └── NorwegianToEnglishTranslator.xml
│   └── CoreLogicExtractor.xml
├── level1.md
└── level1_description.md
"""

def create_template_files():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    created_files = []

    for filename, template in TEMPLATES.items():
        filepath = os.path.join(OUTPUT_DIR, f"{filename}.md")
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files


def main():
    created_files = create_template_files()

    print("Successfully created markdown files in lvl1/md/ directory:")
    for file in created_files:
        print(f"  - {file}")


if __name__ == "__main__":
    main()
