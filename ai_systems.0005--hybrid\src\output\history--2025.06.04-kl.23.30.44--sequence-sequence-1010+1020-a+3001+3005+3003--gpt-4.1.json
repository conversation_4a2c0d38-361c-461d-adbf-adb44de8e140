  {
  "initial_prompt": "Transform into a HYPER-REALISTIC camera fpv\n\nSubject: No human subjects present in this mystical forest scene.\n\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\n\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.",
  "sequence_id": "1010|1020:a|3001|3005|3003",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "Transform into a HYPER-REALISTIC camera fpv\n\nSubject: No human subjects present in this mystical forest scene.\n\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\n\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Hyper-Realistic FPV Camera View of Mystical Emerald Forest Ravine with Mist, Mossy Stream, and Ethereal Atmosphere\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Transform into a HYPER-REALISTIC camera fpv\\n\\nSubject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Hyper-Realistic FPV Camera View of Mystical Emerald Forest Ravine with Mist, Mossy Stream, and Ethereal Atmosphere\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Hyper-Realistic FPV of Mystical Emerald Forest\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Transform into a HYPER-REALISTIC camera fpv\\n\\nSubject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Hyper-Realistic FPV of Mystical Emerald Forest\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Mystical Emerald Forest Scene\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Transform into a HYPER-REALISTIC camera fpv\\n\\nSubject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Mystical Emerald Forest Scene\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Forest Stream\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Transform into a HYPER-REALISTIC camera fpv\\n\\nSubject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"title\": \"Forest Stream\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"renderHyperRealisticFpvMysticalForestStreamNoHumansMonochromaticEmeraldFoggyLighting\"\n}",
        }
      }
    },
      {
      "instruction": "# Bracketed Keyword Infuser\n\nYour goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as:\n\n`{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`",
      "step": "a",
      "title": "Bracketed Keyword Infuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Transform into a HYPER-REALISTIC camera fpv\\n\\nSubject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"function_name\": \"renderHyperRealisticFpvMysticalForestStreamNoHumansMonochromaticEmeraldFoggyLighting\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_description\": \"Transform into a HYPER-REALISTIC camera fpv [fpv] [hyper-realistic] [no_humans]. Subject: No human subjects present in this mystical forest scene [subject:no_humans]. Scene: A winding stream [stream:serpentine][water:reflective] cuts through moss-covered rocks [rocks:moss_covered][moss:vivid_texture] and emerald grass [grass:emerald][color:monochromatic_green], creating a serpentine path through a deep forest ravine [camera_path:along_stream][leading_lines:strong]. Towering trees [tree:bare_branches][trees:towering][height:emphasized] emerge from steep, moss-blanketed slopes [slope:steep][moss:thick], while a thick mist [atmosphere:thick_mist][fog:layered][depth:multi_layered] hovers in the distance [distance:depth_emphasis], creating layers of depth through the gorge [depth:layered][gorge:deep]. Style: Ethereal fantasy photography [style:ethereal_fantasy][fantasy:mystical] with a monochromatic green color palette [palette:emerald][color_scheme:monochromatic]. Atmospheric fog [fog:atmospheric][mood:mysterious] creates depth and mystery [mystery:heightened] while soft, diffused lighting [light:soft_diffused][lighting:filtered_canopy][texture:highlighted] filters through the canopy, highlighting the textures of moss and water [texture_emphasis:moss_water]. Dreamy, ethereal, mystical, atmospheric, monochromatic [mood:dreamy][emotion:ethereal_mystical_atmospheric][color:monochromatic_emerald].\"\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Pattern Analyzer\n\nYour goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as:\n\n`{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`",
      "step": "a",
      "title": "Syntactic Pattern Analyzer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Transform into a HYPER-REALISTIC camera fpv\\n\\nSubject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"enhanced_description\": \"Transform into a HYPER-REALISTIC camera fpv [fpv] [hyper-realistic] [no_humans]. Subject: No human subjects present in this mystical forest scene [subject:no_humans]. Scene: A winding stream [stream:serpentine][water:reflective] cuts through moss-covered rocks [rocks:moss_covered][moss:vivid_texture] and emerald grass [grass:emerald][color:monochromatic_green], creating a serpentine path through a deep forest ravine [camera_path:along_stream][leading_lines:strong]. Towering trees [tree:bare_branches][trees:towering][height:emphasized] emerge from steep, moss-blanketed slopes [slope:steep][moss:thick], while a thick mist [atmosphere:thick_mist][fog:layered][depth:multi_layered] hovers in the distance [distance:depth_emphasis], creating layers of depth through the gorge [depth:layered][gorge:deep]. Style: Ethereal fantasy photography [style:ethereal_fantasy][fantasy:mystical] with a monochromatic green color palette [palette:emerald][color_scheme:monochromatic]. Atmospheric fog [fog:atmospheric][mood:mysterious] creates depth and mystery [mystery:heightened] while soft, diffused lighting [light:soft_diffused][lighting:filtered_canopy][texture:highlighted] filters through the canopy, highlighting the textures of moss and water [texture_emphasis:moss_water]. Dreamy, ethereal, mystical, atmospheric, monochromatic [mood:dreamy][emotion:ethereal_mystical_atmospheric][color:monochromatic_emerald].\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Overall prompt is structured as a transformation request followed by a set of sectional descriptions.\",\n      \"Sections are labeled with descriptors (e.g., Subject:, Scene:, Style:) followed by detailed prose.\",\n      \"Each description within sections contains descriptive phrases, often listing features.\",\n      \"Enhanced description interleaves original prose with inline keyword-tag markers inside brackets.\",\n      \"Formatting makes use of colons for section separation and brackets for attribute tagging.\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Section label followed by colon and free-form description: Section_Label: Description\",\n      \"Within descriptions, attributes/entities are extracted and associated with bracketed tags: [keyword:attribute]\",\n      \"General structure for attributes: [entity:descriptor] or [category:qualified_descriptor]\",\n      \"Multiple keywords can be chained or standalone in a description.\",\n      \"Descriptive modifiers (e.g., hyper-realistic, no_humans) may appear as bracketed flags: [flag]\"\n    ],\n    \"map_syntax_rules\": [\n      \"Each major description area (Subject, Scene, Style, etc.) is treated as a block, optionally with subtitle.\",\n      \"Within blocks, natural language is interspersed with attribute tags.\",\n      \"Attribute tags correspond to prominent descriptive features, either nouns or adjectives.\",\n      \"Content following colons is expanded with bracketed tagged keywords referencing key attributes.\",\n      \"Bracketed tags are placed immediately after the relevant description or appended as global flags at the start.\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Brackets [] denote attribute tagging or syntactic entity marking.\",\n      \"Colon ':' separates section labels from descriptions.\",\n      \"Newline or paragraph breaks delineate major sections.\",\n      \"Quotation marks used for prompt encapsulation in input, removed in enhanced description.\"\n    ],\n    \"define_parameter_structures\": [\n      \"Section_Label: (arbitrary string) — represents various contextual blocks.\",\n      \"[key:descriptor] — represents extracted descriptive attributes.\",\n      \"[flag] — represents global modifiers or context markers.\",\n      \"Parameters are placed adjacent to or after the entities/concepts they modify.\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"Any descriptive text divided into sections can be transformed by extracting the main entities and their qualifying attributes.\",\n      \"For each key entity or feature in the narrative, generate a bracketed attribute tag using the [entity:descriptor] schema.\",\n      \"Block-level attributes can be summarized as global flags at the start.\",\n      \"Sectional prose remains, now augmented with structured markers for syntactic features.\",\n      \"Formatting remains consistent regardless of semantic content or theme.\"\n    ],\n    \"codify_transformation_rules\": [\n      \"1. Parse input: Identify major sections by presence of explicit section labels ('Label:') and delineating markers.\",\n      \"2. For each section, extract primary entities/nouns and their descriptive modifiers/adjectives.\",\n      \"3. Generate attribute tags for each entity-modifier pair in the form [entity:qualifier] inserted adjacent to their source in the text.\",\n      \"4. Identify and elevate global context modifiers (such as 'no_humans', 'hyper-realistic', main style keywords) as initial bracketed flags.\",\n      \"5. Preserve original sectional format and descriptive flow, interleaving bracketed syntactic tags as per above.\",\n      \"6. Remove non-essential extraneous formatting (such as enclosing quotes or code block markers) from output.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Prompt Builder\n\nYour goal is not to **describe** visual concepts generally, but to **construct** single-line prompts under 500 characters using bracketed keyword syntax [keyword] and [keyword:value], emphasis markers **text**, and vivid descriptive language to create visual transformation sequences. Execute as:\n\n`{role=syntactic_prompt_builder; input=[visual_concept:str]; process=[identify_transformation_elements(), select_bracketed_keywords(), apply_parameterized_directives(), integrate_emphasis_markers(), construct_vivid_descriptive_text(), ensure_syntactic_compliance(), validate_character_limit(), format_single_line_output()]; constraints=[under_500_character_limit(), bracketed_keyword_syntax_only(), emphasis_marker_integration(), single_line_format(), vivid_verb_requirement()]; requirements=[syntactic_structure_compliance(), visual_transformation_clarity(), keyword_parameter_integration(), descriptive_flow_maintenance()]; output={structured_prompt:str}}`",
      "step": "a",
      "title": "Syntactic Prompt Builder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Transform into a HYPER-REALISTIC camera fpv\\n\\nSubject: No human subjects present in this mystical forest scene.\\n\\nScene: A winding stream cuts through moss-covered rocks and emerald grass, creating a serpentine path through a deep forest ravine. Towering trees with bare branches emerge from steep, moss-blanketed slopes while a thick mist hovers in the distance, creating layers of depth through the gorge.\\n\\nStyle: Ethereal fantasy photography with a monochromatic green color palette dominated by rich emerald tones. Atmospheric fog creates depth and mystery while soft, diffused lighting filters through the canopy, highlighting the textures of moss and water. Dreamy, ethereal, mystical, atmospheric, monochromatic.\\\"```'\\n\\n\",\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Overall prompt is structured as a transformation request followed by a set of sectional descriptions.\",\n      \"Sections are labeled with descriptors (e.g., Subject:, Scene:, Style:) followed by detailed prose.\",\n      \"Each description within sections contains descriptive phrases, often listing features.\",\n      \"Enhanced description interleaves original prose with inline keyword-tag markers inside brackets.\",\n      \"Formatting makes use of colons for section separation and brackets for attribute tagging.\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Section label followed by colon and free-form description: Section_Label: Description\",\n      \"Within descriptions, attributes/entities are extracted and associated with bracketed tags: [keyword:attribute]\",\n      \"General structure for attributes: [entity:descriptor] or [category:qualified_descriptor]\",\n      \"Multiple keywords can be chained or standalone in a description.\",\n      \"Descriptive modifiers (e.g., hyper-realistic, no_humans) may appear as bracketed flags: [flag]\"\n    ],\n    \"map_syntax_rules\": [\n      \"Each major description area (Subject, Scene, Style, etc.) is treated as a block, optionally with subtitle.\",\n      \"Within blocks, natural language is interspersed with attribute tags.\",\n      \"Attribute tags correspond to prominent descriptive features, either nouns or adjectives.\",\n      \"Content following colons is expanded with bracketed tagged keywords referencing key attributes.\",\n      \"Bracketed tags are placed immediately after the relevant description or appended as global flags at the start.\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Brackets [] denote attribute tagging or syntactic entity marking.\",\n      \"Colon ':' separates section labels from descriptions.\",\n      \"Newline or paragraph breaks delineate major sections.\",\n      \"Quotation marks used for prompt encapsulation in input, removed in enhanced description.\"\n    ],\n    \"define_parameter_structures\": [\n      \"Section_Label: (arbitrary string) — represents various contextual blocks.\",\n      \"[key:descriptor] — represents extracted descriptive attributes.\",\n      \"[flag] — represents global modifiers or context markers.\",\n      \"Parameters are placed adjacent to or after the entities/concepts they modify.\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"Any descriptive text divided into sections can be transformed by extracting the main entities and their qualifying attributes.\",\n      \"For each key entity or feature in the narrative, generate a bracketed attribute tag using the [entity:descriptor] schema.\",\n      \"Block-level attributes can be summarized as global flags at the start.\",\n      \"Sectional prose remains, now augmented with structured markers for syntactic features.\",\n      \"Formatting remains consistent regardless of semantic content or theme.\"\n    ],\n    \"codify_transformation_rules\": [\n      \"1. Parse input: Identify major sections by presence of explicit section labels ('Label:') and delineating markers.\",\n      \"2. For each section, extract primary entities/nouns and their descriptive modifiers/adjectives.\",\n      \"3. Generate attribute tags for each entity-modifier pair in the form [entity:qualifier] inserted adjacent to their source in the text.\",\n      \"4. Identify and elevate global context modifiers (such as 'no_humans', 'hyper-realistic', main style keywords) as initial bracketed flags.\",\n      \"5. Preserve original sectional format and descriptive flow, interleaving bracketed syntactic tags as per above.\",\n      \"6. Remove non-essential extraneous formatting (such as enclosing quotes or code block markers) from output.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structured_prompt\": \"[transformation:hyper-realistic][camera:fpv][style:ethereal_fantasy][palette:monochromatic_green][atmosphere:dreamy][atmosphere:mystical][atmosphere:atmospheric][lighting:soft_diffused][depth:mist][humans:none] **Transform** a mystical forest gorge as a winding [stream:serpentine] cuts through [rocks:moss-covered] and [grass:emerald], flanked by [trees:towering][branches:bare] on steep [slopes:moss-blanketed], all **shrouded** by thick [fog:layered_mist] for immersive depth; soft, diffused light **highlights** rich [textures:moss_water] beneath a canopy, evoking a **dreamy, monochromatic emerald** atmosphere.\"\n}",
        }
      }
    }