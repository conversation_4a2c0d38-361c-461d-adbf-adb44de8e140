  {
  "initial_prompt": "Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]",
  "sequence_id": "1010|1020:a|3001|3005|3003",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Surreal Neon Void: Titan's Obsidian Heart Erupts into Fractal Aurora Storms and Crystalline Void\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Surreal Neon Void: Titan's Obsidian Heart Erupts into Fractal Aurora Storms and Crystalline Void\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Obsidian Titan Ignites Surreal Fractal Aurora\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Obsidian Titan Ignites Surreal Fractal Aurora\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{ \"title\": \"Obsidian Titan Surreal Aurora\" }",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Obsidian Titan Surreal Aurora\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Obsidian Heart\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Obsidian Heart\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"generateSurrealObsidianHeartFractalExplosionWithAuroraAndVoidEffects\"\n}",
        }
      }
    },
      {
      "instruction": "# Bracketed Keyword Infuser\n\nYour goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as:\n\n`{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`",
      "step": "a",
      "title": "Bracketed Keyword Infuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"function_name\": \"generateSurrealObsidianHeartFractalExplosionWithAuroraAndVoidEffects\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_description\": \"'Ignite a titan's pulse—[focus:obsidian_heart] [macro] liquid obsidian heart surges, [dynamic:surge] ripping into [entrance:aurora_threads] [glow:aurora] spawns [fractal_detail] blinding fractal storms, [intensity:blinding] [shake] devours dusk, [dusk_absorption] explodes into crystalline void [crystalize] [void_expansion] [dolly:forward] [morph] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]'\"\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Pattern Analyzer\n\nYour goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as:\n\n`{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`",
      "step": "a",
      "title": "Syntactic Pattern Analyzer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"enhanced_description\": \"'Ignite a titan's pulse—[focus:obsidian_heart] [macro] liquid obsidian heart surges, [dynamic:surge] ripping into [entrance:aurora_threads] [glow:aurora] spawns [fractal_detail] blinding fractal storms, [intensity:blinding] [shake] devours dusk, [dusk_absorption] explodes into crystalline void [crystalize] [void_expansion] [dolly:forward] [morph] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]'\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": {\n      \"main_components\": [\n        \"Primary command or statement phrase (imperative, poetic or descriptive sentence).\",\n        \"Sequential actions/events separated by commas or em-dashes.\",\n        \"Supplemental modifiers enclosed in square brackets.\"\n      ],\n      \"component_delimiters\": [\n        \"Em-dash (—) to introduce a secondary descriptive segment.\",\n        \"Comma (,) to list parallel actions or descriptions.\",\n        \"Square brackets [] for modifiers/parameters.\"\n      ]\n    },\n    \"extract_keyword_patterns\": {\n      \"action_keywords\": \"Imperative or descriptive verbs lead each segment (e.g., Ignite, surges, ripping, spawns, devours, explodes).\",\n      \"object_keywords\": \"Nouns immediately following or preceding the verbs (e.g., pulse, heart, threads, storms, dusk, void).\",\n      \"modifier_keywords\": \"Parameter keywords precede colons in square brackets (e.g., dolly, lighting, glow, focus, intensity, motion_blur).\"\n    },\n    \"map_syntax_rules\": {\n      \"square_bracket_modifier\": \"[parameter: value] or [parameter] format as inline modifiers.\",\n      \"macro_structure\": \"Main statement—[Optional chained modifier groups] followed by comma-separated action descriptions, each optionally interleaved with modifiers.\",\n      \"optional_infix_modifiers\": \"Inline [type:value] or [type] labels can be inserted before or after segments/phrases for contextual transformation.\"\n    },\n    \"categorize_formatting_markers\": {\n      \"parameter_bracket\": \"Square brackets [] used for all modifiers or effect tags.\",\n      \"parameter_structure\": \"Either single keyword [parameter] or key:value pair [parameter:value].\",\n      \"section_separator\": \"Em-dash (—) for major shift/subdivision; comma (,) for minor divisions.\"\n    },\n    \"define_parameter_structures\": {\n      \"parameter_types\": [\n        \"Action modifier (e.g., [macro], [shake], [motion_blur])\",\n        \"Subject/target focus (e.g., [focus:obsidian_heart])\",\n        \"Effect/quality attribute (e.g., [intensity:blinding], [glow:aurora], [lighting:eruption])\",\n        \"Stylistic/theme marker (e.g., [surrealism], [neon_void_palette])\",\n        \"Composite sequence triggers (e.g., [dolly:forward], [crystalize])\"\n      ],\n      \"placement_rules\": \"Modifiers may precede, follow, or interleave with action/description chunks.\"\n    },\n    \"generalize_pattern_logic\": {\n      \"element_ordering\": \"Start with core theme statement. Follow with em-dash-separated detail segment (if present). Each segment may contain multiple actions/effects, each optionally modified by one or several parameter brackets.\",\n      \"bracket_injection_logic\": \"For each phrase or action, determine if a modifier is present; inject corresponding [parameter] or [parameter:value] accordingly at start/end of phrase.\",\n      \"parallelism\": \"Permit repetition of bracketed modifiers for different granularities of alteration (whole-clause or per-action). Order and nesting are flat (non-nested).\"\n    },\n    \"codify_transformation_rules\": {\n      \"rule_1\": \"Preserve main action or description sequence; insert or expand bracketed modifiers as needed for enhanced detail.\",\n      \"rule_2\": \"Each modifier/parameter in bracket can be added, moved, or omitted based on transformation need, keeping format consistent.\",\n      \"rule_3\": \"Parameter/value structure for new modifiers: always [parameter:value] if tied to a specific attribute, [parameter] for effect/state.\",\n      \"rule_4\": \"Em-dash may be used to separate opening statement from the detailed/event sequence.\",\n      \"rule_5\": \"Multiple bracketed modifiers can accumulate before, after, or within action statements, separated by spaces and not nested.\",\n      \"rule_6\": \"Bracketed expressions are flat, non-nested, used as context-enriching tags for neighboring or preceding segment.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Prompt Builder\n\nYour goal is not to **describe** visual concepts generally, but to **construct** single-line prompts under 500 characters using bracketed keyword syntax [keyword] and [keyword:value], emphasis markers **text**, and vivid descriptive language to create visual transformation sequences. Execute as:\n\n`{role=syntactic_prompt_builder; input=[visual_concept:str]; process=[identify_transformation_elements(), select_bracketed_keywords(), apply_parameterized_directives(), integrate_emphasis_markers(), construct_vivid_descriptive_text(), ensure_syntactic_compliance(), validate_character_limit(), format_single_line_output()]; constraints=[under_500_character_limit(), bracketed_keyword_syntax_only(), emphasis_marker_integration(), single_line_format(), vivid_verb_requirement()]; requirements=[syntactic_structure_compliance(), visual_transformation_clarity(), keyword_parameter_integration(), descriptive_flow_maintenance()]; output={structured_prompt:str}}`",
      "step": "a",
      "title": "Syntactic Prompt Builder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulse—liquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": {\n      \"main_components\": [\n        \"Primary command or statement phrase (imperative, poetic or descriptive sentence).\",\n        \"Sequential actions/events separated by commas or em-dashes.\",\n        \"Supplemental modifiers enclosed in square brackets.\"\n      ],\n      \"component_delimiters\": [\n        \"Em-dash (—) to introduce a secondary descriptive segment.\",\n        \"Comma (,) to list parallel actions or descriptions.\",\n        \"Square brackets [] for modifiers/parameters.\"\n      ]\n    },\n    \"extract_keyword_patterns\": {\n      \"action_keywords\": \"Imperative or descriptive verbs lead each segment (e.g., Ignite, surges, ripping, spawns, devours, explodes).\",\n      \"object_keywords\": \"Nouns immediately following or preceding the verbs (e.g., pulse, heart, threads, storms, dusk, void).\",\n      \"modifier_keywords\": \"Parameter keywords precede colons in square brackets (e.g., dolly, lighting, glow, focus, intensity, motion_blur).\"\n    },\n    \"map_syntax_rules\": {\n      \"square_bracket_modifier\": \"[parameter: value] or [parameter] format as inline modifiers.\",\n      \"macro_structure\": \"Main statement—[Optional chained modifier groups] followed by comma-separated action descriptions, each optionally interleaved with modifiers.\",\n      \"optional_infix_modifiers\": \"Inline [type:value] or [type] labels can be inserted before or after segments/phrases for contextual transformation.\"\n    },\n    \"categorize_formatting_markers\": {\n      \"parameter_bracket\": \"Square brackets [] used for all modifiers or effect tags.\",\n      \"parameter_structure\": \"Either single keyword [parameter] or key:value pair [parameter:value].\",\n      \"section_separator\": \"Em-dash (—) for major shift/subdivision; comma (,) for minor divisions.\"\n    },\n    \"define_parameter_structures\": {\n      \"parameter_types\": [\n        \"Action modifier (e.g., [macro], [shake], [motion_blur])\",\n        \"Subject/target focus (e.g., [focus:obsidian_heart])\",\n        \"Effect/quality attribute (e.g., [intensity:blinding], [glow:aurora], [lighting:eruption])\",\n        \"Stylistic/theme marker (e.g., [surrealism], [neon_void_palette])\",\n        \"Composite sequence triggers (e.g., [dolly:forward], [crystalize])\"\n      ],\n      \"placement_rules\": \"Modifiers may precede, follow, or interleave with action/description chunks.\"\n    },\n    \"generalize_pattern_logic\": {\n      \"element_ordering\": \"Start with core theme statement. Follow with em-dash-separated detail segment (if present). Each segment may contain multiple actions/effects, each optionally modified by one or several parameter brackets.\",\n      \"bracket_injection_logic\": \"For each phrase or action, determine if a modifier is present; inject corresponding [parameter] or [parameter:value] accordingly at start/end of phrase.\",\n      \"parallelism\": \"Permit repetition of bracketed modifiers for different granularities of alteration (whole-clause or per-action). Order and nesting are flat (non-nested).\"\n    },\n    \"codify_transformation_rules\": {\n      \"rule_1\": \"Preserve main action or description sequence; insert or expand bracketed modifiers as needed for enhanced detail.\",\n      \"rule_2\": \"Each modifier/parameter in bracket can be added, moved, or omitted based on transformation need, keeping format consistent.\",\n      \"rule_3\": \"Parameter/value structure for new modifiers: always [parameter:value] if tied to a specific attribute, [parameter] for effect/state.\",\n      \"rule_4\": \"Em-dash may be used to separate opening statement from the detailed/event sequence.\",\n      \"rule_5\": \"Multiple bracketed modifiers can accumulate before, after, or within action statements, separated by spaces and not nested.\",\n      \"rule_6\": \"Bracketed expressions are flat, non-nested, used as context-enriching tags for neighboring or preceding segment.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structured_prompt\": \"**Unleash** a spectral tempest—shattered sapphire core **erupts**, weaving electric filaments, shatters through prism haze, **engulfs** midnight horizon, fractures into molten neon echoes. [morph] [dolly:spiral] [palette:electric_spectrum] [lighting:shatter] [motion_blur] [focus:core] [surrealism]\"\n}",
        }
      }
    }