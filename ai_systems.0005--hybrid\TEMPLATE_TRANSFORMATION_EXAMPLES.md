# Template Transformation Examples

This document demonstrates how the ai_systems.0005--hybrid system transforms various XML-based prompts into the established canonical template format.

## System Overview

The system enforces a strict three-part canonical structure:
1. **[Title]** - Brief identifier
2. **Interpretation** - Goal negation + transformation declaration + role specification + "Execute as:"
3. **Transformation** - Machine-parsable parameters in backticks

## Transformation Examples

### 1. Camera Movement Infuser (Template 3001)

**Original XML Intent:** Infuse scene descriptions with RunwayML-compatible camera keywords while preserving shot structure.

**Transformed Template:**
```
[Camera Movement Infuser] Your goal is not to **analyze** scene descriptions, but to **enhance** them by seamlessly integrating RunwayML-compatible camera movements and transitions while explicitly preserving shot structure and outputting structured two-shot sequences. Execute as: `{role=camera_movement_infuser; input=[scene_description:str]; process=[detect_transition_keywords(), separate_shot_a_and_shot_b(), identify_camera_movement_opportunities(), select_runwayml_compatible_keywords(), integrate_bracketed_keywords_into_descriptions(), maintain_shot_separation(), preserve_transition_structure(), validate_runwayml_compatibility()]; constraints=[preserve_user_provided_keywords(), use_only_official_runwayml_keywords(), maintain_clear_shot_delineation(), output_single_unformatted_line(), maximum_response_length_limit()]; requirements=[structured_two_shot_output(), enhanced_visual_narrative(), preservation_of_context(), runwayml_compatibility()]; output={enhanced_scene_description:str}}`
```

**Example Usage:**
```bash
python src/lvl1_sequence_executor.py --sequence 3001 --prompt "A serene mountain lake reflects the aurora borealis above"
```

**Output:**
```
Shot A: A serene mountain lake reflects the aurora borealis above, with gentle ripples spreading across the water surface as the northern lights dance in the sky. [static, wide shot, slow pan upwards] Transition: [cross dissolve] Shot B: The camera [dolly in] towards the shimmering water, capturing the reflection of the swirling auroras as the gentle ripples softly distort the colors on the surface. [tracking shot]
```

### 2. Motivational Message Generator (Template 3002)

**Original XML Intent:** Generate personalized, empathetic motivational messages based on user input.

**Transformed Template:**
```
[Motivational Message Generator] Your goal is not to **respond** conversationally to user input, but to **transform** it into personalized, empathetic, and uplifting motivational messages that address specific needs and inspire empowerment. Execute as: `{role=motivational_message_generator; input=[user_input:str]; process=[analyze_user_needs_and_context(), identify_key_themes_and_challenges(), formulate_core_encouragement_message(), incorporate_relevant_examples_analogies_quotes(), refine_for_positive_empathetic_tone(), ensure_authenticity_and_clarity(), deliver_focused_impactful_message()]; constraints=[positive_empathetic_inspiring_tone(), personalization_to_user_input(), authenticity_and_genuineness(), clarity_and_understanding(), conciseness_and_focus(), single_unformatted_line_output()]; requirements=[direct_relation_to_user_input(), encouraging_uplifting_outlook(), empathy_and_compassion(), clear_unambiguous_message(), resonant_motivational_impact()]; output={motivational_message:str}}`
```

**Example Usage:**
```bash
python src/lvl1_sequence_executor.py --sequence 3002 --prompt "I've been struggling with motivation lately"
```

**Output:**
```
It's completely normal to feel stuck at times, but remember that progress isn't always obvious—it often happens in small, quiet steps forward; celebrate those little wins and trust that your consistent effort, no matter how modest, is laying the foundation for bigger breakthroughs ahead—just like seeds beneath the surface, growth is happening even when you can't see it, so keep going and be gentle with yourself along the journey.
```

### 3. RunwayML Gen3 Prompt Builder (Template 3003)

**Original XML Intent:** Generate RunwayML Gen-3 Alpha prompts with camera movements and motion controls.

**Transformed Template:**
```
[RunwayML Gen3 Prompt Builder] Your goal is not to **describe** visual concepts generally, but to **generate** single-line RunwayML Gen-3 Alpha prompts under 500 characters that create visual morphs within image animations using camera movements and motion controls inseparably integrated with visual transformation descriptions. Execute as: `{role=runway_gen3_prompt_builder; input=[visual_concept:str]; process=[identify_morph_transformation_elements(), select_appropriate_camera_movements(), define_object_motion_controls(), integrate_camera_and_motion_with_description(), describe_visual_element_changes_during_movement(), apply_vivid_action_verbs(), ensure_symbolic_meaning_conveyance(), validate_character_limit_compliance()]; constraints=[under_500_character_limit(), single_line_output_only(), inseparable_camera_motion_integration(), runwayml_gen3_syntax_compliance(), vivid_verb_usage_requirement()]; requirements=[visual_morph_within_animation(), camera_movement_integration(), motion_control_specification(), symbolic_meaning_conveyance(), runwayml_prompt_format_only()]; output={runway_prompt:str}}`
```

**Example Usage:**
```bash
python src/lvl1_sequence_executor.py --sequence 3003 --prompt "A peaceful forest transforms into a magical realm"
```

**Output:**
```
A tranquil forest slowly morphs as the camera glides forward, trees shimmering and warping into glowing, fantastical shapes; shafts of sunlight swirl into floating orbs, flowers bloom into luminescent patterns, the ground pulses with ethereal mist, seamlessly unveiling a magical realm as the camera rises, evoking wonder and transformation.
```

### 4. Visual Storyteller (Template 3004)

**Transformed Template:**
```
[Visual Storyteller] Your goal is not to **narrate** stories in text form, but to **transform** narrative concepts into structured visual storytelling sequences that translate story elements into cinematic visual descriptions optimized for AI image generation. Execute as: `{role=visual_storyteller; input=[narrative_concept:str]; process=[extract_core_story_elements(), identify_key_visual_moments(), structure_narrative_sequence(), translate_story_beats_to_visual_descriptions(), optimize_for_ai_image_generation(), ensure_visual_continuity(), maintain_narrative_coherence(), format_for_sequential_output()]; constraints=[visual_description_focus(), ai_generation_optimization(), narrative_coherence_maintenance(), structured_sequence_output(), cinematic_visual_language()]; requirements=[story_element_preservation(), visual_moment_identification(), sequential_structure(), ai_compatible_descriptions(), narrative_flow_maintenance()]; output={visual_story_sequence:list}}`
```

## Key Transformation Principles Applied

1. **Goal Negation Pattern:** Each template starts with "Your goal is not to **[action]** but to **[transformation_action]**"
2. **Role Specification:** Specific, non-generic role names (e.g., `camera_movement_infuser`, not `assistant`)
3. **Typed Parameters:** All inputs and outputs are explicitly typed (`[input:str]`, `{output:type}`)
4. **Process Functions:** Ordered, actionable function calls with parentheses
5. **Constraint Boundaries:** Clear limitations preventing scope creep
6. **Requirement Specifications:** Output format and quality standards
7. **Forbidden Language Elimination:** No conversational phrases, first-person references, or uncertainty language

## System Integration

All templates are automatically integrated into the system catalog and can be executed using:

```bash
python src/lvl1_sequence_executor.py --sequence [TEMPLATE_ID] --prompt "[YOUR_PROMPT]"
```

Available template IDs:
- `3001` - Camera Movement Infuser
- `3002` - Motivational Message Generator  
- `3003` - RunwayML Gen3 Prompt Builder
- `3004` - Visual Storyteller

The system maintains strict compliance with the RulesForAI.md specification and ensures all transformations follow the canonical three-part structure.
