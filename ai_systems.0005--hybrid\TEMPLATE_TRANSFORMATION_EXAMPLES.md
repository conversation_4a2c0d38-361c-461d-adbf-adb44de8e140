# Template Transformation Examples

This document demonstrates how the ai_systems.0005--hybrid system transforms various XML-based prompts into the established canonical template format, with emphasis on **syntactic structure** rather than non-knowable references.

## System Overview

The system enforces a strict three-part canonical structure:
1. **[Title]** - Brief identifier
2. **Interpretation** - Goal negation + transformation declaration + role specification + "Execute as:"
3. **Transformation** - Machine-parsable parameters in backticks

## Core Principle: Syntactic Focus

The system focuses on **syntactic patterns** rather than domain-specific knowledge. For example, instead of referencing "RunwayML-compatible keywords," it analyzes the underlying syntax pattern:

**Example Syntax Pattern:**
```
[fpv] [lighting_change:warm] A mesmerizing **field of bioluminescent flowers** reveals fiery orange, dazzling pink, and mystical purple hues.
```

**Syntactic Elements Identified:**
- `[keyword]` - Bracketed directives
- `[keyword:value]` - Parameterized directives
- `**text**` - Emphasis markers
- Descriptive text with vivid verbs

## Transformation Examples

### 1. Bracketed Keyword Infuser (Template 3001)

**Syntactic Focus:** Integrate bracketed camera keywords using `[keyword]` and `[keyword:value]` syntax patterns.

**Transformed Template:**
```
[Bracketed Keyword Infuser] Your goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as: `{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`
```

**Example Usage:**
```bash
python src/lvl1_sequence_executor.py --sequence 3001 --prompt "A serene mountain lake reflects the aurora borealis above"
```

**Output:**
```
A serene [mountain lake] [reflects] the [aurora borealis] [above], with [gentle ripples] [spreading] across the [water surface] as the [northern lights] [dance] in the [sky]. [focus:lake reflection] [exposure:long] [angle:wide] [movement:slow_pan:upward] [emphasis:color_saturation] [time:night]
```

### 2. Motivational Message Generator (Template 3002)

**Original XML Intent:** Generate personalized, empathetic motivational messages based on user input.

**Transformed Template:**
```
[Motivational Message Generator] Your goal is not to **respond** conversationally to user input, but to **transform** it into personalized, empathetic, and uplifting motivational messages that address specific needs and inspire empowerment. Execute as: `{role=motivational_message_generator; input=[user_input:str]; process=[analyze_user_needs_and_context(), identify_key_themes_and_challenges(), formulate_core_encouragement_message(), incorporate_relevant_examples_analogies_quotes(), refine_for_positive_empathetic_tone(), ensure_authenticity_and_clarity(), deliver_focused_impactful_message()]; constraints=[positive_empathetic_inspiring_tone(), personalization_to_user_input(), authenticity_and_genuineness(), clarity_and_understanding(), conciseness_and_focus(), single_unformatted_line_output()]; requirements=[direct_relation_to_user_input(), encouraging_uplifting_outlook(), empathy_and_compassion(), clear_unambiguous_message(), resonant_motivational_impact()]; output={motivational_message:str}}`
```

**Example Usage:**
```bash
python src/lvl1_sequence_executor.py --sequence 3002 --prompt "I've been struggling with motivation lately"
```

**Output:**
```
It's completely normal to feel stuck at times, but remember that progress isn't always obvious—it often happens in small, quiet steps forward; celebrate those little wins and trust that your consistent effort, no matter how modest, is laying the foundation for bigger breakthroughs ahead—just like seeds beneath the surface, growth is happening even when you can't see it, so keep going and be gentle with yourself along the journey.
```

### 3. Syntactic Prompt Builder (Template 3003)

**Syntactic Focus:** Construct prompts using bracketed keyword syntax `[keyword]` and `[keyword:value]`, emphasis markers `**text**`, and vivid descriptive language.

**Transformed Template:**
```
[Syntactic Prompt Builder] Your goal is not to **describe** visual concepts generally, but to **construct** single-line prompts under 500 characters using bracketed keyword syntax [keyword] and [keyword:value], emphasis markers **text**, and vivid descriptive language to create visual transformation sequences. Execute as: `{role=syntactic_prompt_builder; input=[visual_concept:str]; process=[identify_transformation_elements(), select_bracketed_keywords(), apply_parameterized_directives(), integrate_emphasis_markers(), construct_vivid_descriptive_text(), ensure_syntactic_compliance(), validate_character_limit(), format_single_line_output()]; constraints=[under_500_character_limit(), bracketed_keyword_syntax_only(), emphasis_marker_integration(), single_line_format(), vivid_verb_requirement()]; requirements=[syntactic_structure_compliance(), visual_transformation_clarity(), keyword_parameter_integration(), descriptive_flow_maintenance()]; output={structured_prompt:str}}`
```

**Example Usage:**
```bash
python src/lvl1_sequence_executor.py --sequence 3003 --prompt "A peaceful forest transforms into a magical realm"
```

**Output:**
```
[scene:forest][mood:peaceful] bathed in soft golden light, **gradually transforms** as ethereal [magic:aura][glow:iridescent] weaves through towering trees, vibrant [flora:bioluminescent][creatures:fantastical] emerge, and enchanted mist unfurls, shifting the woodland into a **breathtaking magical realm** alive with shimmering wonder
```

### 4. Syntactic Pattern Analyzer (Template 3005)

**Syntactic Focus:** Extract and codify underlying syntactic structure patterns into generalized transformation rules.

**Transformed Template:**
```
[Syntactic Pattern Analyzer] Your goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as: `{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`
```

**Example Usage:**
```bash
python src/lvl1_sequence_executor.py --sequence 3005 --prompt "[fpv] [lighting_change:warm] A mesmerizing **field of bioluminescent flowers** reveals fiery orange, dazzling pink, and mystical purple hues."
```

**Output:** (Structured analysis of syntactic patterns including bracketed directives, emphasis markers, and descriptive text patterns)

### 4. Visual Storyteller (Template 3004)

**Transformed Template:**
```
[Visual Storyteller] Your goal is not to **narrate** stories in text form, but to **transform** narrative concepts into structured visual storytelling sequences that translate story elements into cinematic visual descriptions optimized for AI image generation. Execute as: `{role=visual_storyteller; input=[narrative_concept:str]; process=[extract_core_story_elements(), identify_key_visual_moments(), structure_narrative_sequence(), translate_story_beats_to_visual_descriptions(), optimize_for_ai_image_generation(), ensure_visual_continuity(), maintain_narrative_coherence(), format_for_sequential_output()]; constraints=[visual_description_focus(), ai_generation_optimization(), narrative_coherence_maintenance(), structured_sequence_output(), cinematic_visual_language()]; requirements=[story_element_preservation(), visual_moment_identification(), sequential_structure(), ai_compatible_descriptions(), narrative_flow_maintenance()]; output={visual_story_sequence:list}}`
```

## Key Transformation Principles Applied

1. **Goal Negation Pattern:** Each template starts with "Your goal is not to **[action]** but to **[transformation_action]**"
2. **Role Specification:** Specific, non-generic role names (e.g., `camera_movement_infuser`, not `assistant`)
3. **Typed Parameters:** All inputs and outputs are explicitly typed (`[input:str]`, `{output:type}`)
4. **Process Functions:** Ordered, actionable function calls with parentheses
5. **Constraint Boundaries:** Clear limitations preventing scope creep
6. **Requirement Specifications:** Output format and quality standards
7. **Forbidden Language Elimination:** No conversational phrases, first-person references, or uncertainty language

## System Integration

All templates are automatically integrated into the system catalog and can be executed using:

```bash
python src/lvl1_sequence_executor.py --sequence [TEMPLATE_ID] --prompt "[YOUR_PROMPT]"
```

Available template IDs:
- `3001` - Camera Movement Infuser
- `3002` - Motivational Message Generator  
- `3003` - RunwayML Gen3 Prompt Builder
- `3004` - Visual Storyteller

The system maintains strict compliance with the RulesForAI.md specification and ensures all transformations follow the canonical three-part structure.
