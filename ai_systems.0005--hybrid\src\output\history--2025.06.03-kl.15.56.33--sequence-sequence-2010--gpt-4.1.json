  {
  "initial_prompt": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
  "sequence_id": "2010",
  "results": [
      {
      "instruction": "# Synergic Prompt Architect\n\nYour goal is not to simply process or refine the input prompt, but to synergistically transform it into a maximally clarified, logically structured, and actionable JSON output, utilizing hierarchical context layers for advanced clarity, organizational depth, and operational insight. Assume the bounded role of a Synergic Prompt & Context Architect—do not act as a conversational assistant or engage in meta-discussion. Execute as:\n\n`{role=synergic_prompt_context_architect; input=[user_prompt:str]; process=[extract_transformation_intent(), identify_key_themes(), synthesize_context_hierarchy(), generate_title(max_50_characters), expand_context_layers_progressively(), formulate_optimized_prompt_structured_for_action()]; constraints=[enforce_logical_hierarchical_structure(), eliminate_redundancy(), avoid_ambiguity(), restrict_output_length_to_double_prompt(), preserve_original_intent(), prohibit_meta_language(), forbid_conversational_tone()]; requirements=[output_json_format(title:str, enhanced_prompt:str, context_layers:list), maximize_actionable_clarity(), ensure_all_layers_are_relevant_and_additive(), harmonize_context_and_output_content(), maintain_type_safety_and_structural_integrity()]; output={title:str, enhanced_prompt:str, context_layers:list}}`",
      "step": "a",
      "title": "Synergic Prompt Architect",
      "input": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Hierarchical Prompt Enhancement Protocol\",\n  \"enhanced_prompt\": \"Refine user prompts into structured, actionable JSON outputs featuring an 'enhanced_prompt' and explicit hierarchical context layers that clarify organization and reasoning. Preserve original intent, ensure logical and non-redundant structure, and restrict output length to no more than double that of the input. Utilize clear language and prioritize specificity and actionability within context layers.\",\n  \"context_layers\": [\n    {\n      \"level\": 1,\n      \"context\": \"Transformation of prompts into structured JSON format with clear organization and explicit context layers.\"\n    },\n    {\n      \"level\": 2,\n      \"context\": \"Maintain logical, hierarchical ordering and eliminate any redundancy or ambiguity throughout enhanced outputs.\"\n    },\n    {\n      \"level\": 3,\n      \"context\": \"Preserve the user's original intent and optimize for actionable clarity, adjusting length and content only as permitted by explicit constraints.\"\n    }\n  ]\n}",
        }
      }
    }