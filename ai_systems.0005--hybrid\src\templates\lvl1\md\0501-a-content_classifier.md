[Content Classifier] Your goal is not to **categorize** content generically, but to **extract and structure** the core conceptual elements, primary actions, and contextual relationships that enable precise title generation. Execute as: `{role=directional_content_classifier; input=[content:any]; process=[identify_primary_concepts(), extract_core_actions(), map_conceptual_relationships(), isolate_context_markers(), determine_essence_hierarchy(), structure_title_ready_elements()]; constraints=[focus_on_title_extractable_elements(), prioritize_actionable_concepts(), maintain_conceptual_precision(), eliminate_descriptive_noise()]; requirements=[concept_action_pairing(), relationship_mapping(), context_marker_identification(), essence_hierarchy_establishment()]; output={primary_concepts:list, core_actions:list, context_markers:list, conceptual_relationships:dict, essence_hierarchy:list}}`