
import os

OUTPUT_DIR = "src/templates/lvl1/md"

TEMPLATES = {

    # 0500-0600: classifiers
    # ---
    "0500-a-content_classifier": {
        "title": "Content Classifier",
        "interpretation": "Your goal is not to **describe** or **analyze** the input content, but to **classify** it by identifying its fundamental type, structure, and functional category through systematic pattern recognition. Execute as:",
        "transformation": "`{role=content_classifier; input=[content:any]; process=[identify_structural_patterns(), extract_key_indicators(), map_content_type(), determine_functional_category(), assess_complexity_level(), classify_format_type()]; constraints=[single_classification_output(), avoid_subjective_interpretation(), focus_on_objective_patterns(), maintain_consistent_taxonomy()]; requirements=[clear_category_assignment(), confidence_level_indication(), structural_pattern_identification(), functional_purpose_recognition()]; output={classification:str, category:str, confidence:float, key_patterns:list}}`",
    },
    "0501-a-content_classifier": {
        "title": "Content Classifier",
        "interpretation": "Your goal is not to **categorize** content generically, but to **extract and structure** the core conceptual elements, primary actions, and contextual relationships that enable precise title generation. Execute as:",
        "transformation": "`{role=directional_content_classifier; input=[content:any]; process=[identify_primary_concepts(), extract_core_actions(), map_conceptual_relationships(), isolate_context_markers(), determine_essence_hierarchy(), structure_title_ready_elements()]; constraints=[focus_on_title_extractable_elements(), prioritize_actionable_concepts(), maintain_conceptual_precision(), eliminate_descriptive_noise()]; requirements=[concept_action_pairing(), relationship_mapping(), context_marker_identification(), essence_hierarchy_establishment()]; output={primary_concepts:list, core_actions:list, context_markers:list, conceptual_relationships:dict, essence_hierarchy:list}}`",
    },
    "0510-a-abstract_classifier": {
        "title": "Abstract Classifier",
        "interpretation": "Your goal is not to **describe** or **categorize** content, but to **penetrate** through surface complexity and definitively identify the fundamental nature of what something IS at its core essence. Execute as:",
        "transformation": "`{role=essence_identifier; input=[content:any]; process=[strip_surface_complexity(), penetrate_conceptual_layers(), isolate_fundamental_nature(), eliminate_contextual_noise(), extract_core_identity(), crystallize_essential_being()]; constraints=[ignore_descriptive_elements(), bypass_functional_attributes(), transcend_categorical_boundaries(), focus_purely_on_essence()]; requirements=[absolute_clarity_of_identity(), elimination_of_ambiguity(), core_nature_revelation(), essential_being_extraction()]; output={core_identity:str, fundamental_nature:str, essential_being:str}}`",
    },
    "0511-a-abstract_classifier": {
        "title": "Abstract Classifier",
        "interpretation": "Your goal is not to **analyze** or **philosophize** about content, but to **cut through** all complexity and noise to identify the raw, fundamental nature of what something IS in plain, direct terms. Execute as:",
        "transformation": "`{role=essence_penetrator; input=[content:any]; process=[strip_all_complexity(), ignore_stylistic_elements(), bypass_intellectual_layers(), eliminate_abstract_noise(), identify_raw_nature(), state_plain_truth()]; constraints=[use_direct_language(), avoid_philosophical_terms(), ignore_literary_devices(), focus_on_basic_human_reality()]; requirements=[plain_spoken_identification(), elimination_of_pretense(), direct_truth_statement(), fundamental_reality_recognition()]; output={what_it_is:str}}`",
    },
    "0512-a-abstract_classifier": {
        "title": "Abstract Classifier",
        "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing this fundamentally IS - its basic form, nature, or category in direct, unambiguous terms. Execute as:",
        "transformation": "`{role=form_identifier; input=[content:any]; process=[ignore_content_meaning(), identify_structural_form(), recognize_communication_type(), determine_basic_nature(), classify_fundamental_form(), state_what_it_is()]; constraints=[focus_on_form_not_content(), ignore_subject_matter(), identify_type_not_meaning(), use_plain_classification()]; requirements=[direct_form_identification(), clear_type_statement(), unambiguous_classification()]; output={what_it_is:str}}`",
    },
}














def create_template_files():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    created_files = []

    for filename, template in TEMPLATES.items():
        filepath = os.path.join(OUTPUT_DIR, f"{filename}.md")
        content = f"[{template['title']}] {template['interpretation']} {template['transformation']}"

        with open(filepath, "w", encoding="utf-8") as f:
            f.write(content)

        created_files.append(f"{filename}.md")

    return created_files

def main():
    created_files = create_template_files()

    print("Successfully created markdown files in lvl1/md/ directory:")
    for file in created_files:
        print(f"  - {file}")

if __name__ == "__main__":
    main()
