  {
  "initial_prompt": "Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]",
  "sequence_id": "1010|1020:a|3001|3005|3003",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Surreal Neon Void: Titan's Obsidian Heart Ignites Fractal Storm in Crystalline Aurora\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Surreal Neon Void: Titan's Obsidian Heart Ignites Fractal Storm in Crystalline Aurora\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Titan's Obsidian Heart Ignites Neon Fractal Storm\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Titan's Obsidian Heart Ignites Neon Fractal Storm\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Obsidian Heart Fractal Storm\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Obsidian Heart Fractal Storm\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Obsidian Storm\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Obsidian Storm\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"function_name\": \"generateObsidianTitanFractalStormVisualWithAuroraAndSurrealNeonEffects\"\n}",
        }
      }
    },
      {
      "instruction": "# Bracketed Keyword Infuser\n\nYour goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as:\n\n`{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`",
      "step": "a",
      "title": "Bracketed Keyword Infuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"function_name\": \"generateObsidianTitanFractalStormVisualWithAuroraAndSurrealNeonEffects\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_description\": \"\\\"Ignite a titan's [subject:central_focus] pulseliquid obsidian heart [material:liquid_obsidian] surges [dynamic_energy], [camera:close_up] ripping into aurora threads [color:aurora_palette] [motion:shred] [motion_blur], spawns blinding fractal storms [effect:fractal] [lighting:intense] [particle_effect:storm] [dolly:forward], devours dusk [time:twilight] [environment:dusk], explodes into crystalline void [effect:explosion] [crystal_shards] [background:void] [lighting:eruption] [palette:neon_void_palette], [morph] [surrealism]\\\"\"\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Pattern Analyzer\n\nYour goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as:\n\n`{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`",
      "step": "a",
      "title": "Syntactic Pattern Analyzer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"enhanced_description\": \"\\\"Ignite a titan's [subject:central_focus] pulseliquid obsidian heart [material:liquid_obsidian] surges [dynamic_energy], [camera:close_up] ripping into aurora threads [color:aurora_palette] [motion:shred] [motion_blur], spawns blinding fractal storms [effect:fractal] [lighting:intense] [particle_effect:storm] [dolly:forward], devours dusk [time:twilight] [environment:dusk], explodes into crystalline void [effect:explosion] [crystal_shards] [background:void] [lighting:eruption] [palette:neon_void_palette], [morph] [surrealism]\\\"\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Input is a descriptive sentence or phrase conveying a sequence of actions, subjects, or effects.\",\n      \"Elements are separated by commas or occasionally standalone bracketed markers.\",\n      \"Several embedded modifiers, descriptors, or effect labels are present in square brackets, following or preceding core content.\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Verbs are used at the start of clauses (e.g., Ignite, surges, ripping, spawns, devours, explodes).\",\n      \"Bracketed tags label or augment description (e.g., [morph], [lighting:eruption], [neon_void_palette]).\",\n      \"Colon structure within brackets signifies property/feature mappings (e.g., [lighting:eruption]).\",\n      \"Multiple bracketed tags can follow a clause or phrase.\"\n    ],\n    \"map_syntax_rules\": [\n      \"Clause = [Action verb] + [Subject/Descriptor] (optional: + [Additional description]) + [Optional: bracketed tags]\",\n      \"Clauses are separated by commas.\",\n      \"Bracketed tags directly after or within the clause they modify.\",\n      \"Standalone bracketed tags are permitted at the end or in-line.\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Double quotes or triple backticks encapsulate the overall input.\",\n      \"Square brackets enclose labels, tags, or modifier tokens.\",\n      \"Brackets may contain single identifiers or colon-separated key:value label pairs.\",\n      \"Optional inline tags denote feature, effect, or classification.\"\n    ],\n    \"define_parameter_structures\": [\n      \"Bracketed tag = '[' + (key | key:value) + ']'\",\n      \"Multiple tags separated by whitespace.\",\n      \"Within a phrase, tags may provide attribute information (e.g., [material:liquid_obsidian], [lighting:intense]).\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"Structure allows for flowing, action-oriented description with modular, insertable attribute tags.\",\n      \"Verb-led clauses describe dynamic states/events.\",\n      \"Modifiers in brackets layer additional detail or stylistic instruction.\",\n      \"Bracketed tags can be expanded, replaced, or appended while maintaining base syntax.\"\n    ],\n    \"codify_transformation_rules\": [\n      \"Given any descriptive action/scene, break into clauses where each clause contains an action, subject, and optional details.\",\n      \"For each clause, append zero or more attribute tags in square brackets, formatted as [key] or [key:value], as appropriate to the clause.\",\n      \"Maintain comma-separated sequencing between clauses.\",\n      \"Bracketed markers can be reordered, augmented, or edited without changing the underlying clause structure.\",\n      \"Place bracketed tags after the main clause or sentence fragment to indicate associated attributes, effects, or modifiers.\",\n      \"Encapsulate entire sequence in double quotes or backticks as per desired output format.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Prompt Builder\n\nYour goal is not to **describe** visual concepts generally, but to **construct** single-line prompts under 500 characters using bracketed keyword syntax [keyword] and [keyword:value], emphasis markers **text**, and vivid descriptive language to create visual transformation sequences. Execute as:\n\n`{role=syntactic_prompt_builder; input=[visual_concept:str]; process=[identify_transformation_elements(), select_bracketed_keywords(), apply_parameterized_directives(), integrate_emphasis_markers(), construct_vivid_descriptive_text(), ensure_syntactic_compliance(), validate_character_limit(), format_single_line_output()]; constraints=[under_500_character_limit(), bracketed_keyword_syntax_only(), emphasis_marker_integration(), single_line_format(), vivid_verb_requirement()]; requirements=[syntactic_structure_compliance(), visual_transformation_clarity(), keyword_parameter_integration(), descriptive_flow_maintenance()]; output={structured_prompt:str}}`",
      "step": "a",
      "title": "Syntactic Prompt Builder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Input is a descriptive sentence or phrase conveying a sequence of actions, subjects, or effects.\",\n      \"Elements are separated by commas or occasionally standalone bracketed markers.\",\n      \"Several embedded modifiers, descriptors, or effect labels are present in square brackets, following or preceding core content.\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Verbs are used at the start of clauses (e.g., Ignite, surges, ripping, spawns, devours, explodes).\",\n      \"Bracketed tags label or augment description (e.g., [morph], [lighting:eruption], [neon_void_palette]).\",\n      \"Colon structure within brackets signifies property/feature mappings (e.g., [lighting:eruption]).\",\n      \"Multiple bracketed tags can follow a clause or phrase.\"\n    ],\n    \"map_syntax_rules\": [\n      \"Clause = [Action verb] + [Subject/Descriptor] (optional: + [Additional description]) + [Optional: bracketed tags]\",\n      \"Clauses are separated by commas.\",\n      \"Bracketed tags directly after or within the clause they modify.\",\n      \"Standalone bracketed tags are permitted at the end or in-line.\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Double quotes or triple backticks encapsulate the overall input.\",\n      \"Square brackets enclose labels, tags, or modifier tokens.\",\n      \"Brackets may contain single identifiers or colon-separated key:value label pairs.\",\n      \"Optional inline tags denote feature, effect, or classification.\"\n    ],\n    \"define_parameter_structures\": [\n      \"Bracketed tag = '[' + (key | key:value) + ']'\",\n      \"Multiple tags separated by whitespace.\",\n      \"Within a phrase, tags may provide attribute information (e.g., [material:liquid_obsidian], [lighting:intense]).\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"Structure allows for flowing, action-oriented description with modular, insertable attribute tags.\",\n      \"Verb-led clauses describe dynamic states/events.\",\n      \"Modifiers in brackets layer additional detail or stylistic instruction.\",\n      \"Bracketed tags can be expanded, replaced, or appended while maintaining base syntax.\"\n    ],\n    \"codify_transformation_rules\": [\n      \"Given any descriptive action/scene, break into clauses where each clause contains an action, subject, and optional details.\",\n      \"For each clause, append zero or more attribute tags in square brackets, formatted as [key] or [key:value], as appropriate to the clause.\",\n      \"Maintain comma-separated sequencing between clauses.\",\n      \"Bracketed markers can be reordered, augmented, or edited without changing the underlying clause structure.\",\n      \"Place bracketed tags after the main clause or sentence fragment to indicate associated attributes, effects, or modifiers.\",\n      \"Encapsulate entire sequence in double quotes or backticks as per desired output format.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structured_prompt\": \"\\\"Unleash **celestial winds** as midnight petals swirl, weaving luminous sigils across cracked marble, breathe veins of molten sapphire into frozen shadows, shatter silence with radiant meteor blooms, unravel starlight tapestries into prismatic cascades. [morph] [dolly:orbit] [palette:eclipse] [lighting:corona] [motion_blur] [surrealism]\\\"\"\n}",
        }
      }
    }