  {
  "initial_prompt": "Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]",
  "sequence_id": "1010|1020:a|3001|3005|3003",
  "results": [
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Surreal Neon Eruption: Obsidian-Hearted Titan Ripping Aurora Into Fractal Void Storms\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Surreal Neon Eruption: Obsidian-Hearted Titan Ripping Aurora Into Fractal Void Storms\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Obsidian Titan Ignites Neon Fractal Storm\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Obsidian Titan Ignites Neon Fractal Storm\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Obsidian Titan Ignition\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Obsidian Titan Ignition\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Titan Ignition\"}",
        }
      }
    },
      {
      "instruction": "# Function Namer\n\nYour goal is not to **describe** the input, but to **transform** it into a descriptive camelCase function name that captures the complete action, context, and parameters. Execute as:\n\n`{role=comprehensive_function_namer; input=[text:str]; process=[identify_primary_action(), extract_target_objects(), include_context_modifiers(), determine_parameter_hints(), synthesize_descriptive_function_name(format=camelCase, max_words=10)]; constraints=[bridgeConsciousnessGap(), amplifyMessageImpactWhilePreservingMeaning(), maximizeLLMeffectiveness(), ensureComprehensiveGuidance()];output={function_name:str}}`",
      "step": "a",
      "title": "Function Namer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"title\": \"Titan Ignition\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"function_name\":\"animateTitanIgnitionWithObsidianHeartAuroraFractalsAndSurrealLighting\"}",
        }
      }
    },
      {
      "instruction": "# Bracketed Keyword Infuser\n\nYour goal is not to **analyze** scene descriptions, but to **enhance** them by integrating bracketed camera keywords and parameterized directives using the syntax pattern [keyword] and [keyword:value] while preserving shot structure and visual narrative flow. Execute as:\n\n`{role=bracketed_keyword_infuser; input=[scene_description:str]; process=[identify_visual_elements_for_emphasis(), detect_camera_movement_opportunities(), apply_bracketed_keyword_syntax(), integrate_parameterized_directives(), preserve_narrative_flow(), maintain_shot_structure(), format_with_emphasis_markers()]; constraints=[use_bracketed_syntax_only(), preserve_existing_keywords(), maintain_descriptive_text_integrity(), single_line_output()]; requirements=[syntactic_keyword_integration(), visual_emphasis_preservation(), camera_movement_enhancement(), structured_output_format()]; output={enhanced_description:str}}`",
      "step": "a",
      "title": "Bracketed Keyword Infuser",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"function_name\": \"animateTitanIgnitionWithObsidianHeartAuroraFractalsAndSurrealLighting\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_description\": \"\\\"Ignite a titan's [closeup] [focus:heart] pulseliquid obsidian heart surges, [texture:liquid_obsidian] [lighting:intense] ripping into [camera_move:whip_pan:left] aurora threads, [vibrancy:high] [color:aurora] spawns blinding [flash] fractal storms, [particle_effect:fractal] devours dusk, [time_lapse] explodes into [wide_shot] crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"\"\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Pattern Analyzer\n\nYour goal is not to **interpret** the meaning of input text, but to **extract** and **codify** its underlying syntactic structure patterns into generalized rules that can be applied to transform other inputs using the same structural framework. Execute as:\n\n`{role=syntactic_pattern_analyzer; input=[example_text:str]; process=[identify_structural_elements(), extract_keyword_patterns(), map_syntax_rules(), categorize_formatting_markers(), define_parameter_structures(), generalize_pattern_logic(), codify_transformation_rules()]; constraints=[focus_on_syntax_not_semantics(), extract_generalizable_patterns(), avoid_domain_specific_references(), maintain_structural_abstraction()]; requirements=[pattern_rule_extraction(), syntactic_structure_mapping(), generalizable_transformation_logic(), abstracted_formatting_rules()]; output={syntactic_pattern_rules:dict}}`",
      "step": "a",
      "title": "Syntactic Pattern Analyzer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"enhanced_description\": \"\\\"Ignite a titan's [closeup] [focus:heart] pulseliquid obsidian heart surges, [texture:liquid_obsidian] [lighting:intense] ripping into [camera_move:whip_pan:left] aurora threads, [vibrancy:high] [color:aurora] spawns blinding [flash] fractal storms, [particle_effect:fractal] devours dusk, [time_lapse] explodes into [wide_shot] crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Input is a single descriptive sentence or sequence of actions, separated by commas, ending with a period.\",\n      \"Bracketed tokens/enclosures are placed at specific positions within or between descriptive phrases.\",\n      \"Commas appear as delimiters for each clause/segment.\",\n      \"Descriptive language and modifiers are intertwined with bracketed parameter markers.\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Verbs in imperative or active tense (e.g., ignite, surges, ripping, spawns, devours, explodes)\",\n      \"Descriptive noun phrases (e.g., titan's pulseliquid obsidian heart, aurora threads, fractal storms, crystalline void)\",\n      \"Bracketed parameter notations (e.g., [morph], [dolly:forward], [lighting:intense])\"\n    ],\n    \"map_syntax_rules\": [\n      \"Phrase construction: [VERB] [NOUN_PHRASE], [VERB] [NOUN_PHRASE], ...\",\n      \"Bracketed tags are embedded: before, within, or after noun/verb phrases for parameterization.\",\n      \"Bracketed parameters use syntax: [type] or [type:value] and are placed contextually relative to the phrase they modify.\",\n      \"Multiple bracketed tags can co-exist inside a single clause.\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Square brackets are used exclusively for parameter/feature tagging.\",\n      \"Colons are used to separate parameter types and values inside brackets (e.g., [camera_move:whip_pan:left]).\",\n      \"No quotation marks or parentheses are used in the descriptive body (unless verbatim quotes are required by context).\"\n    ],\n    \"define_parameter_structures\": [\n      \"[PARAMETER_TYPE]\", \n      \"[PARAMETER_TYPE:VALUE]\",\n      \"[PARAMETER_TYPE:VALUE:SUBVALUE]\",\n      \"Parameter tags can be chained together in any order.\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"For each descriptive phrase or action, optionally inject zero or more bracketed parameter tags relevant to the described content or effect.\",\n      \"Parameters can precede, follow, or be embedded within noun/verb phrases.\",\n      \"Major clauses remain separated by commas, final clause ends with a period.\",\n      \"Base narrative sequence is preserved, enhancement occurs via structural parameter tagging.\"\n    ],\n    \"codify_transformation_rules\": [\n      \"Identify key action/description clauses; do not alter their order.\",\n      \"For each clause, optionally enhance by inserting '[PARAMETER]' tags denoting features—anywhere within or after the clause.\",\n      \"Each '[PARAMETER]' follows the syntactic structure: [type] or [type:value] or [type:value:subvalue], where 'type' is abstracted.\",\n      \"Commas separate phrases; each clause can carry multiple bracketed tags.\",\n      \"Entire sequence concludes with terminal period.\",\n      \"Maintain descriptive phrase backbone; transformation is additive via bracket insertion based on context or desired attribute.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Syntactic Prompt Builder\n\nYour goal is not to **describe** visual concepts generally, but to **construct** single-line prompts under 500 characters using bracketed keyword syntax [keyword] and [keyword:value], emphasis markers **text**, and vivid descriptive language to create visual transformation sequences. Execute as:\n\n`{role=syntactic_prompt_builder; input=[visual_concept:str]; process=[identify_transformation_elements(), select_bracketed_keywords(), apply_parameterized_directives(), integrate_emphasis_markers(), construct_vivid_descriptive_text(), ensure_syntactic_compliance(), validate_character_limit(), format_single_line_output()]; constraints=[under_500_character_limit(), bracketed_keyword_syntax_only(), emphasis_marker_integration(), single_line_format(), vivid_verb_requirement()]; requirements=[syntactic_structure_compliance(), visual_transformation_clarity(), keyword_parameter_integration(), descriptive_flow_maintenance()]; output={structured_prompt:str}}`",
      "step": "a",
      "title": "Syntactic Prompt Builder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Ignite a titan's pulseliquid obsidian heart surges, ripping into aurora threads, spawns blinding fractal storms, devours dusk, explodes into crystalline void. [morph] [dolly:forward] [neon_void_palette] [lighting:eruption] [motion_blur] [surrealism]\\\"```'\\n\\n\",\n  \"syntactic_pattern_rules\": {\n    \"identify_structural_elements\": [\n      \"Input is a single descriptive sentence or sequence of actions, separated by commas, ending with a period.\",\n      \"Bracketed tokens/enclosures are placed at specific positions within or between descriptive phrases.\",\n      \"Commas appear as delimiters for each clause/segment.\",\n      \"Descriptive language and modifiers are intertwined with bracketed parameter markers.\"\n    ],\n    \"extract_keyword_patterns\": [\n      \"Verbs in imperative or active tense (e.g., ignite, surges, ripping, spawns, devours, explodes)\",\n      \"Descriptive noun phrases (e.g., titan's pulseliquid obsidian heart, aurora threads, fractal storms, crystalline void)\",\n      \"Bracketed parameter notations (e.g., [morph], [dolly:forward], [lighting:intense])\"\n    ],\n    \"map_syntax_rules\": [\n      \"Phrase construction: [VERB] [NOUN_PHRASE], [VERB] [NOUN_PHRASE], ...\",\n      \"Bracketed tags are embedded: before, within, or after noun/verb phrases for parameterization.\",\n      \"Bracketed parameters use syntax: [type] or [type:value] and are placed contextually relative to the phrase they modify.\",\n      \"Multiple bracketed tags can co-exist inside a single clause.\"\n    ],\n    \"categorize_formatting_markers\": [\n      \"Square brackets are used exclusively for parameter/feature tagging.\",\n      \"Colons are used to separate parameter types and values inside brackets (e.g., [camera_move:whip_pan:left]).\",\n      \"No quotation marks or parentheses are used in the descriptive body (unless verbatim quotes are required by context).\"\n    ],\n    \"define_parameter_structures\": [\n      \"[PARAMETER_TYPE]\",\n      \"[PARAMETER_TYPE:VALUE]\",\n      \"[PARAMETER_TYPE:VALUE:SUBVALUE]\",\n      \"Parameter tags can be chained together in any order.\"\n    ],\n    \"generalize_pattern_logic\": [\n      \"For each descriptive phrase or action, optionally inject zero or more bracketed parameter tags relevant to the described content or effect.\",\n      \"Parameters can precede, follow, or be embedded within noun/verb phrases.\",\n      \"Major clauses remain separated by commas, final clause ends with a period.\",\n      \"Base narrative sequence is preserved, enhancement occurs via structural parameter tagging.\"\n    ],\n    \"codify_transformation_rules\": [\n      \"Identify key action/description clauses; do not alter their order.\",\n      \"For each clause, optionally enhance by inserting '[PARAMETER]' tags denoting features—anywhere within or after the clause.\",\n      \"Each '[PARAMETER]' follows the syntactic structure: [type] or [type:value] or [type:value:subvalue], where 'type' is abstracted.\",\n      \"Commas separate phrases; each clause can carry multiple bracketed tags.\",\n      \"Entire sequence concludes with terminal period.\",\n      \"Maintain descriptive phrase backbone; transformation is additive via bracket insertion based on context or desired attribute.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"structured_prompt\": \"[surge] **celestial flame** rips through [void_silk], shatters into [supernova:fractal] halos [lighting:inferno], engulfs [sky_mirage], unfurls into [prismatic:vortex] storms, devours **eventide**, bursts into [glass:nebulae] [transmute], [motion_streaks] [camera:glide:vertical], [palette:spectral_night], [hyper-realism].\"\n}",
        }
      }
    }