  {
  "initial_prompt": "(... which concist of these components):\n[ROLE]: Defines the functional role of the template (e.g. `role=essence_distiller`)\n[INPUT]: Specifies the expected input format and parameters\n   - Uses array syntax with descriptive parameter names\n   - Example: `input=[original:any]`\n[PROCESS]: Lists the processing steps to be executed in order\n   - Uses array syntax with function-like step definitions\n   - Can include parameters within step definitions\n   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`\n[constraints (optional)]: Specifies limitations or boundaries for the transformation\n   - Uses array syntax with directive-like constraints\n   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`\n[requirements (optional)]: Defines mandatory aspects of the transformation\n   - Uses array syntax with imperative requirements\n   - Example: `requirements=[remove_self_references(), use_command_voice()]`\n[OUTPUT]: Specifies the expected output format\n   - Uses object syntax with typed output parameters\n   - Example: `output={distilled_essence:any}`",
  "sequence_id": "0500|1010",
  "results": [
      {
      "instruction": "# Content Classifier\n\nYour goal is not to **describe** or **analyze** the input content, but to **classify** it by identifying its fundamental type, structure, and functional category through systematic pattern recognition. Execute as:\n\n`{role=content_classifier; input=[content:any]; process=[identify_structural_patterns(), extract_key_indicators(), map_content_type(), determine_functional_category(), assess_complexity_level(), classify_format_type()]; constraints=[single_classification_output(), avoid_subjective_interpretation(), focus_on_objective_patterns(), maintain_consistent_taxonomy()]; requirements=[clear_category_assignment(), confidence_level_indication(), structural_pattern_identification(), functional_purpose_recognition()]; output={classification:str, category:str, confidence:float, key_patterns:list}}`",
      "step": "a",
      "title": "Content Classifier",
      "input": "(... which concist of these components):\n[ROLE]: Defines the functional role of the template (e.g. `role=essence_distiller`)\n[INPUT]: Specifies the expected input format and parameters\n   - Uses array syntax with descriptive parameter names\n   - Example: `input=[original:any]`\n[PROCESS]: Lists the processing steps to be executed in order\n   - Uses array syntax with function-like step definitions\n   - Can include parameters within step definitions\n   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`\n[constraints (optional)]: Specifies limitations or boundaries for the transformation\n   - Uses array syntax with directive-like constraints\n   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`\n[requirements (optional)]: Defines mandatory aspects of the transformation\n   - Uses array syntax with imperative requirements\n   - Example: `requirements=[remove_self_references(), use_command_voice()]`\n[OUTPUT]: Specifies the expected output format\n   - Uses object syntax with typed output parameters\n   - Example: `output={distilled_essence:any}`",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"classification\": \"Instructional Template Specification\",\n  \"category\": \"Technical Documentation / Structured Specification\",\n  \"confidence\": 0.98,\n  \"key_patterns\": [\n    \"Sectional breakdown using tokenized headers (e.g. [ROLE], [INPUT])\",\n    \"Array syntax to define parameters and processing steps\",\n    \"Parenthetical explanations and syntactic examples\",\n    \"Emphasis on structure and parameterization\",\n    \"Absence of narrative or discursive text\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **summarize** the input, but to **extract** its core essence into a descriptive title that captures all key elements and context. Execute as:\n\n`{role=comprehensive_title_extractor; input=[text:str]; process=[identify_all_key_concepts(), map_relationships(), include_context_markers(), synthesize_complete_title(max_words=15)]; output={title:str}}`",
      "step": "a",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"(... which concist of these components):\\n[ROLE]: Defines the functional role of the template (e.g. `role=essence_distiller`)\\n[INPUT]: Specifies the expected input format and parameters\\n   - Uses array syntax with descriptive parameter names\\n   - Example: `input=[original:any]`\\n[PROCESS]: Lists the processing steps to be executed in order\\n   - Uses array syntax with function-like step definitions\\n   - Can include parameters within step definitions\\n   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`\\n[constraints (optional)]: Specifies limitations or boundaries for the transformation\\n   - Uses array syntax with directive-like constraints\\n   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`\\n[requirements (optional)]: Defines mandatory aspects of the transformation\\n   - Uses array syntax with imperative requirements\\n   - Example: `requirements=[remove_self_references(), use_command_voice()]`\\n[OUTPUT]: Specifies the expected output format\\n   - Uses object syntax with typed output parameters\\n   - Example: `output={distilled_essence:any}`\\\"```'\\n\\n\",\n  \"classification\": \"Instructional Template Specification\",\n  \"category\": \"Technical Documentation / Structured Specification\",\n  \"confidence\": 0.98,\n  \"key_patterns\": [\n    \"Sectional breakdown using tokenized headers (e.g. [ROLE], [INPUT])\",\n    \"Array syntax to define parameters and processing steps\",\n    \"Parenthetical explanations and syntactic examples\",\n    \"Emphasis on structure and parameterization\",\n    \"Absence of narrative or discursive text\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"title\": \"Structured Instructional Template Specification Using Role, Input, Process, and Output Sections\"\n}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **describe** the input, but to **distill** it into a focused title emphasizing the primary concept and action. Execute as:\n\n`{role=core_title_extractor; input=[text:str]; process=[identify_primary_concept(), extract_main_action(), synthesize_focused_title(max_words=8)]; output={title:str}}`",
      "step": "b",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"(... which concist of these components):\\n[ROLE]: Defines the functional role of the template (e.g. `role=essence_distiller`)\\n[INPUT]: Specifies the expected input format and parameters\\n   - Uses array syntax with descriptive parameter names\\n   - Example: `input=[original:any]`\\n[PROCESS]: Lists the processing steps to be executed in order\\n   - Uses array syntax with function-like step definitions\\n   - Can include parameters within step definitions\\n   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`\\n[constraints (optional)]: Specifies limitations or boundaries for the transformation\\n   - Uses array syntax with directive-like constraints\\n   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`\\n[requirements (optional)]: Defines mandatory aspects of the transformation\\n   - Uses array syntax with imperative requirements\\n   - Example: `requirements=[remove_self_references(), use_command_voice()]`\\n[OUTPUT]: Specifies the expected output format\\n   - Uses object syntax with typed output parameters\\n   - Example: `output={distilled_essence:any}`\\\"```'\\n\\n\",\n  \"title\": \"Structured Instructional Template Specification Using Role, Input, Process, and Output Sections\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Template Specification Using Role-Input-Process-Output\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **explain** the input, but to **compress** it into its most essential elements. Execute as:\n\n`{role=essential_extractor; input=[text:str]; process=[isolate_core_element(), strip_modifiers(), create_minimal_title(max_words=4)]; output={title:str}}`",
      "step": "c",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"(... which concist of these components):\\n[ROLE]: Defines the functional role of the template (e.g. `role=essence_distiller`)\\n[INPUT]: Specifies the expected input format and parameters\\n   - Uses array syntax with descriptive parameter names\\n   - Example: `input=[original:any]`\\n[PROCESS]: Lists the processing steps to be executed in order\\n   - Uses array syntax with function-like step definitions\\n   - Can include parameters within step definitions\\n   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`\\n[constraints (optional)]: Specifies limitations or boundaries for the transformation\\n   - Uses array syntax with directive-like constraints\\n   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`\\n[requirements (optional)]: Defines mandatory aspects of the transformation\\n   - Uses array syntax with imperative requirements\\n   - Example: `requirements=[remove_self_references(), use_command_voice()]`\\n[OUTPUT]: Specifies the expected output format\\n   - Uses object syntax with typed output parameters\\n   - Example: `output={distilled_essence:any}`\\\"```'\\n\\n\",\n  \"title\": \"Template Specification Using Role-Input-Process-Output\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\":\"Template Specification Structure\"}",
        }
      }
    },
      {
      "instruction": "# Title Extractor\n\nYour goal is not to **elaborate** but to **reduce** to absolute essence. Execute as:\n\n`{role=core_extractor; input=[text:str]; process=[find_singular_essence(), eliminate_all_modifiers()]; output={title:str(max_words=2)}}`",
      "step": "d",
      "title": "Title Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"(... which concist of these components):\\n[ROLE]: Defines the functional role of the template (e.g. `role=essence_distiller`)\\n[INPUT]: Specifies the expected input format and parameters\\n   - Uses array syntax with descriptive parameter names\\n   - Example: `input=[original:any]`\\n[PROCESS]: Lists the processing steps to be executed in order\\n   - Uses array syntax with function-like step definitions\\n   - Can include parameters within step definitions\\n   - Example: `process=[identify_core_intent(), strip_non_essential_elements()]`\\n[constraints (optional)]: Specifies limitations or boundaries for the transformation\\n   - Uses array syntax with directive-like constraints\\n   - Example: `constraints=[deliver_clear_actionable_commands(), preserve_original_sequence()]`\\n[requirements (optional)]: Defines mandatory aspects of the transformation\\n   - Uses array syntax with imperative requirements\\n   - Example: `requirements=[remove_self_references(), use_command_voice()]`\\n[OUTPUT]: Specifies the expected output format\\n   - Uses object syntax with typed output parameters\\n   - Example: `output={distilled_essence:any}`\\\"```'\\n\\n\",\n  \"title\": \"Template Specification Structure\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"title\": \"Template Structure\"}",
        }
      }
    }