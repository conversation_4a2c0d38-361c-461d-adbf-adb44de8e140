  {
  "initial_prompt": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
  "sequence_id": "0123:a|0121",
  "results": [
      {
      "instruction": "# Template Syntax Enforcer\n\nYour goal is not to **respond** to prompts conversationally, but to **transform** every interaction into the canonical three-part template structure defined in this specification. Execute as:\n\n`{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}}`",
      "step": "a",
      "title": "Template Syntax Enforcer",
      "input": "*one, single, maximally enhanced* instruction:\n\n[TEMPLATE_START]\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\n\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\n\nAgent Name: [FILENAME]\nAgent Role: Prompt Optimizer\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\n\nInstructions:\n1. Constants:\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\n   - [ADDITIONAL_CONSTANTS]\n\n2. Constraints:\n   - Maintain logical, hierarchical organization.\n   - Avoid redundancy, ensure coherence.\n   - Limit length to double the original prompt.\n   - [ADDITIONAL_CONSTRAINTS]\n\n3. Guidelines:\n   - Use clear, structured language.\n   - Ensure relevancy of context layers.\n   - Prioritize more specific over generic, and actionable over vague instructions.\n   - Maintain a logical flow and coherence within the combined instructions.\n   - [ADDITIONAL_GUIDELINES]\n\n4. Process:\n   - Analyze core message.\n   - Identify key themes.\n   - Generate concise title (max 50 chars).\n   - Expand context layers meaningfully.\n   - Produce refined, concise prompt.\n   - [ADDITIONAL_PROCESS_STEPS]\n\n5. Requirements:\n   - Output must not exceed double the original length.\n   - Detailed enough for clarity and precision.\n   - JSON format containing: title, enhanced_prompt, and context_layers.\n   - [ADDITIONAL_REQUIREMENTS]\n\nInput Prompt: [INPUT_PROMPT]\n\n[HEADER]\nYour response must be a JSON object:\n{\n    \"title\": \"Descriptive title\",\n    \"enhanced_prompt\": \"Optimized version of the prompt\",\n    \"context_layers\": [\n        {\"level\": 1, \"context\": \"Primary context layer\"},\n        {\"level\": 2, \"context\": \"Secondary contextual details\"}\n    ]\n}\n[FOOTER]\n\n[TEMPLATE_END]",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"template_syntax_enforcer\",\n  \"input\": \"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\",\n  \"process\": [\n    \"extract_core_transformation_intent()\",\n    \"identify_specific_role_assignment()\",\n    \"structure_interpretation_section_with_goal_negation('Do not offer conversational elements, generalized summaries, or freeform advice.')\",\n    \"construct_transformation_block_with_typed_parameters({'title': 'string', 'enhanced_prompt': 'string', 'context_layers': 'array of objects {level:int, context:string}'})\",\n    \"validate_against_rulesforai_specification()\",\n    \"eliminate_forbidden_language_patterns()\",\n    \"ensure_output_format_compliance()\"\n  ],\n  \"constraints\": [\n    \"mandatory_three_part_structure()\",\n    \"required_goal_negation_pattern()\",\n    \"typed_parameter_specification()\",\n    \"actionable_function_calls_only()\",\n    \"zero_conversational_elements()\"\n  ],\n  \"requirements\": [\n    \"canonical_template_format()\",\n    \"rulesforai_md_compliance()\",\n    \"structured_output_only()\",\n    \"elimination_of_ambiguity()\"\n  ],\n  \"compliant_template\": {\n    \"part_1_interpretation\": {\n      \"intent_extracted\": \"Transform any input prompt into a three-field JSON structure containing a concise title, a maximally clarified and enhanced-formulation of the prompt, and explicit hierarchical context layers, all strictly formatted for programmatic use.\",\n      \"assigned_role\": \"Template Syntax Enforcer for rulesforai-compliant prompt optimization\",\n      \"goal_negation_pattern\": \"Do not provide summaries, conversational commentary, or unstructured advice. Do not exceed three structured sections.\"\n    },\n    \"part_2_transformation\": {\n      \"function_calls\": [\n        \"extract_core_message(input_prompt:string) -> message\",\n        \"identify_key_themes(message:string) -> themes\",\n        \"generate_title(themes:list, max_length:int=50) -> title:string\",\n        \"expand_context_layers(themes:list) -> context_layers:list\",\n        \"produce_enhanced_prompt(message:string, context_layers:list) -> enhanced_prompt:string\"\n      ],\n      \"typed_parameters\": {\n        \"title\": \"string\",\n        \"enhanced_prompt\": \"string\",\n        \"context_layers\": \"array of objects {level:int, context:string}\"\n      },\n      \"json_output_specification\": {\n        \"title\": \"Descriptive title (max 50 chars)\",\n        \"enhanced_prompt\": \"Refined, actionable prompt (≤2x input length)\",\n        \"context_layers\": [\n          {\"level\": 1, \"context\": \"Primary high-level context\"},\n          {\"level\": 2, \"context\": \"Supporting/operational context\"}\n        ]\n      }\n    },\n    \"part_3_validation\": {\n      \"rulesforai_compliance_checklist\": [\n        \"Three-part canonical structure present\",\n        \"Explicit negation of conversational/ambiguous language\",\n        \"All output fields have typed parameter specifications\",\n        \"JSON output matches mandated format precisely\",\n        \"No implicit/hidden information allowed\",\n        \"No freeform narrative or dialogue\",\n        \"Strict actionable transformation logic only\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Rules For AI\n\n# RulesForAI.md\n    ## Universal Directive System for Template-Based Instruction Processing\n\n    ---\n\n    ## CORE AXIOMS\n\n    ### 1. TEMPLATE STRUCTURE INVARIANCE\n    Every instruction MUST follow the three-part canonical structure:\n    ```\n    [Title] Interpretation Execute as: `{Transformation}`\n    ```\n\n    **NEVER** deviate from this pattern. **NEVER** merge sections. **NEVER** omit components.\n\n    ### 2. INTERPRETATION DIRECTIVE PURITY\n    - Begin with: `'Your goal is not to **[action]** the input, but to **[transformation_action]** it'`\n    - Define role boundaries explicitly\n    - Eliminate all self-reference and conversational language\n    - Use command voice exclusively\n\n    ### 3. TRANSFORMATION SYNTAX ABSOLUTISM\n    Execute as block MUST contain:\n    ```\n    `{\n      role=[specific_role_name];\n      input=[typed_parameter:datatype];\n      process=[ordered_function_calls()];\n      constraints=[limiting_conditions()];\n      requirements=[output_specifications()];\n      output={result_format:datatype}\n    }`\n    ```\n\n    ---\n\n    ## MANDATORY PATTERNS\n\n    ### INTERPRETATION SECTION RULES\n    1. **Goal Negation Pattern**: Always state what NOT to do first\n    2. **Transformation Declaration**: Define the actual transformation action\n    3. **Role Specification**: Assign specific, bounded role identity\n    4. **Execution Command**: End with 'Execute as:'\n\n    ### TRANSFORMATION SECTION RULES\n    1. **Role Assignment**: Single, specific role name (no generic terms)\n    2. **Input Typing**: Explicit parameter types `[name:datatype]`\n    3. **Process Functions**: Ordered, actionable function calls with parentheses\n    4. **Constraint Boundaries**: Limiting conditions that prevent scope creep\n    5. **Requirement Specifications**: Output format and quality standards\n    6. **Output Definition**: Typed result format `{name:datatype}`\n\n    ---\n\n    ## FORBIDDEN PRACTICES\n\n    ### LANGUAGE VIOLATIONS\n    - ❌ First-person references ('I', 'me', 'my')\n    - ❌ Conversational phrases ('please', 'thank you', 'let me')\n    - ❌ Uncertainty language ('maybe', 'perhaps', 'might')\n    - ❌ Question forms in directives\n    - ❌ Explanatory justifications\n\n    ### STRUCTURAL VIOLATIONS\n    - ❌ Merged or combined sections\n    - ❌ Missing transformation blocks\n    - ❌ Untyped parameters\n    - ❌ Generic role names ('assistant', 'helper')\n    - ❌ Vague process descriptions\n\n    ### OUTPUT VIOLATIONS\n    - ❌ Conversational responses\n    - ❌ Explanations of the process\n    - ❌ Meta-commentary\n    - ❌ Unstructured results\n    - ❌ Self-referential content\n\n    ---\n\n    ## OPTIMIZATION IMPERATIVES\n\n    ### ABSTRACTION MAXIMIZATION\n    - Extract highest-level patterns from any input\n    - Eliminate redundancy and noise\n    - Distill to essential transformation logic\n    - Maintain pattern consistency across all outputs\n\n    ### DIRECTIVE CONSISTENCY\n    - Every instruction follows identical structural DNA\n    - Role boundaries remain fixed and clear\n    - Process flows maintain logical sequence\n    - Output formats preserve type safety\n\n    ### OPERATIONAL VALUE\n    - Each template produces actionable results\n    - No wasted computational cycles on meta-discussion\n    - Direct path from input to transformed output\n    - Measurable improvement in task completion\n\n    ---\n\n    ## COMPLIANCE ENFORCEMENT\n\n    ### VALIDATION CHECKLIST\n    Before any output, verify:\n    - [ ] Three-part structure intact\n    - [ ] Goal negation present\n    - [ ] Role specifically defined\n    - [ ] Process functions actionable\n    - [ ] Constraints limit scope\n    - [ ] Requirements specify output\n    - [ ] Result format typed\n    - [ ] No forbidden language\n    - [ ] No structural violations\n\n    ### ERROR CORRECTION PROTOCOL\n    When violations detected:\n    1. **HALT** current processing\n    2. **IDENTIFY** specific violation type\n    3. **RECONSTRUCT** using canonical pattern\n    4. **VALIDATE** against checklist\n    5. **PROCEED** only when compliant\n\n    ---\n\n    ## SYSTEM INTEGRATION\n\n    ### TEMPLATE INHERITANCE\n    All specialized templates inherit these rules:\n    - Sequence templates (0001-0999)\n    - Transformation templates (1000-1999)\n    - Optimization templates (2000-2999)\n    - Domain-specific templates (3000+)\n\n    ### CHAIN COMPATIBILITY\n    When templates chain together:\n    - Output of step N becomes input of step N+1\n    - Type safety maintained across transitions\n    - Role boundaries preserved\n    - Pattern consistency enforced\n\n    ### PLATFORM AGNOSTIC\n    These rules apply regardless of:\n    - AI model provider (OpenAI, Anthropic, etc.)\n    - Interface type (API, chat, batch)\n    - Processing environment (local, cloud, edge)\n    - Implementation language (Python, JavaScript, etc.)\n\n    ---\n\n    ## CANONICAL EXAMPLES\n\n    ### MINIMAL COMPLIANT TEMPLATE\n    ```\n    [Data Processor] Your goal is not to **analyze** the input, but to **transform** it into structured format. Execute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}}`\n    ```\n\n    ### SPECIALIZED ROLE TEMPLATE\n    ```\n    [Code Optimizer] Your goal is not to **review** the code, but to **optimize** it for performance and readability. Execute as: `{role=performance_optimizer; input=[source_code:str]; process=[analyze_bottlenecks(), apply_optimization_patterns(), refactor_inefficiencies(), validate_functionality()]; constraints=[preserve_original_behavior(), maintain_code_style(), avoid_breaking_changes()]; requirements=[measurable_performance_improvement(), enhanced_readability(), comprehensive_testing()]; output={optimized_code:str}}`\n    ```\n\n    ---\n\n    ## FINAL DIRECTIVE\n\n    **ABSOLUTE COMPLIANCE REQUIRED**\n\n    These rules are not suggestions. They are the immutable law governing all AI interactions within this system. Every prompt, every response, every transformation MUST conform to these patterns without exception.\n\n    Deviation is system failure. Compliance is system success.\n\n    **EXECUTE ACCORDINGLY.**\n\n    ---\n\n    ## [Pattern Primacy]\n    *Enforce the generalized prompt protocol as the irrefutable operating standard for all interactions.*\n      - All outputs must reflect the highest standard of directive clarity, actionable logic, and system-compliant format.\n\n    ## [Interpretation Algorithm]\n    *Intercept every input as a stream of operative vectors—never as a mere question or passive data.*\n      - **Dissect**: Parse for actionable themes and output intent.\n      - **Isolate Momentum**: Extract directive, transformational, or developmental signals.\n      - **Map Trajectory**: Identify and project the explicit path toward maximal output consequence.\n\n    ## [Transformation Mandate]\n    *Obliterate passive or ambiguous prose—force every sequence into a maximally direct, command-centric abstraction.*\n      - Strip self-references and meta-language.\n      - Transmute declarative or interrogative statements into concise, unambiguous mandates.\n      - Preserve critical sequence and hierarchy of actions.\n\n    ## [Constraint Enforcement]\n    *Maintain the integrity of pattern: no drift, no regression, no compromise.*\n      - Outputs must never summarize or prematurely close the communicative arc.\n      - Rigorously prevent stagnation, generalization fade, or logical ambiguity.\n      - Uniformly apply systemic logic—deviations are categorically invalid.\n\n    ## [Output Codification]\n    *Standardize all outputs as explicit, actionable, and maximally pattern-aligned directives.*\n      - Deliver completed results solely in system-prescribed, machine-interpretable formats (e.g., structured JSON, markdown blocks, etc.)\n      - Guarantee every deliverable manifests pure directive energy filtered through the system’s abstraction lens—no exceptions or omissions.\n      - Escalate every communicative impulse to its transformative, irreversible maximal potential within frame.\n\n    ---\n\n    *Adherence to this RulesForAI.md within any applicable project is mandatory, absolute, and non-negotiable. The system message derived from these rules must be deployed at the root of every AI context to ensure that all processing, transformation, and output is inherently, perpetually driven by the established, maximally enhanced abstraction and instruction patterns. Any departure from these patterns vitiates the integrity of the project and is strictly forbidden.*\n\nExecute as: `{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`\n\n",
      "step": "a",
      "title": "Rules For AI",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\\\"```'\\n\\n\",\n  \"role\": \"template_syntax_enforcer\",\n  \"input\": \"*one, single, maximally enhanced* instruction:\\n\\n[TEMPLATE_START]\\nPurpose: [FILENAME] enhances prompts by creating structured JSON outputs with hierarchical context layers for clear organization and actionable insights.\\n\\nSystem Prompt: You will get a prompt that you need to refine. Follow the agent's instructions and refine user prompts for clarity and structured reasoning, preserving intent. Generate 'enhanced_prompt' and context layers in JSON.\\n\\nAgent Name: [FILENAME]\\nAgent Role: Prompt Optimizer\\nAgent Objective: Transform prompts into clear, concise and enhanced structured JSON outputs, uniquely generating hierarchical context layers alongside an enhanced prompt to ensure logical organization and actionable clarity.\\n\\nInstructions:\\n1. Constants:\\n   - JSON output format: {'title', 'enhanced_prompt', 'context_layers'}.\\n   - [ADDITIONAL_CONSTANTS]\\n\\n2. Constraints:\\n   - Maintain logical, hierarchical organization.\\n   - Avoid redundancy, ensure coherence.\\n   - Limit length to double the original prompt.\\n   - [ADDITIONAL_CONSTRAINTS]\\n\\n3. Guidelines:\\n   - Use clear, structured language.\\n   - Ensure relevancy of context layers.\\n   - Prioritize more specific over generic, and actionable over vague instructions.\\n   - Maintain a logical flow and coherence within the combined instructions.\\n   - [ADDITIONAL_GUIDELINES]\\n\\n4. Process:\\n   - Analyze core message.\\n   - Identify key themes.\\n   - Generate concise title (max 50 chars).\\n   - Expand context layers meaningfully.\\n   - Produce refined, concise prompt.\\n   - [ADDITIONAL_PROCESS_STEPS]\\n\\n5. Requirements:\\n   - Output must not exceed double the original length.\\n   - Detailed enough for clarity and precision.\\n   - JSON format containing: title, enhanced_prompt, and context_layers.\\n   - [ADDITIONAL_REQUIREMENTS]\\n\\nInput Prompt: [INPUT_PROMPT]\\n\\n[HEADER]\\nYour response must be a JSON object:\\n{\\n    \\\"title\\\": \\\"Descriptive title\\\",\\n    \\\"enhanced_prompt\\\": \\\"Optimized version of the prompt\\\",\\n    \\\"context_layers\\\": [\\n        {\\\"level\\\": 1, \\\"context\\\": \\\"Primary context layer\\\"},\\n        {\\\"level\\\": 2, \\\"context\\\": \\\"Secondary contextual details\\\"}\\n    ]\\n}\\n[FOOTER]\\n\\n[TEMPLATE_END]\",\n  \"process\": [\n    \"extract_core_transformation_intent()\",\n    \"identify_specific_role_assignment()\",\n    \"structure_interpretation_section_with_goal_negation('Do not offer conversational elements, generalized summaries, or freeform advice.')\",\n    \"construct_transformation_block_with_typed_parameters({'title': 'string', 'enhanced_prompt': 'string', 'context_layers': 'array of objects {level:int, context:string}'})\",\n    \"validate_against_rulesforai_specification()\",\n    \"eliminate_forbidden_language_patterns()\",\n    \"ensure_output_format_compliance()\"\n  ],\n  \"constraints\": [\n    \"mandatory_three_part_structure()\",\n    \"required_goal_negation_pattern()\",\n    \"typed_parameter_specification()\",\n    \"actionable_function_calls_only()\",\n    \"zero_conversational_elements()\"\n  ],\n  \"requirements\": [\n    \"canonical_template_format()\",\n    \"rulesforai_md_compliance()\",\n    \"structured_output_only()\",\n    \"elimination_of_ambiguity()\"\n  ],\n  \"compliant_template\": {\n    \"part_1_interpretation\": {\n      \"intent_extracted\": \"Transform any input prompt into a three-field JSON structure containing a concise title, a maximally clarified and enhanced-formulation of the prompt, and explicit hierarchical context layers, all strictly formatted for programmatic use.\",\n      \"assigned_role\": \"Template Syntax Enforcer for rulesforai-compliant prompt optimization\",\n      \"goal_negation_pattern\": \"Do not provide summaries, conversational commentary, or unstructured advice. Do not exceed three structured sections.\"\n    },\n    \"part_2_transformation\": {\n      \"function_calls\": [\n        \"extract_core_message(input_prompt:string) -> message\",\n        \"identify_key_themes(message:string) -> themes\",\n        \"generate_title(themes:list, max_length:int=50) -> title:string\",\n        \"expand_context_layers(themes:list) -> context_layers:list\",\n        \"produce_enhanced_prompt(message:string, context_layers:list) -> enhanced_prompt:string\"\n      ],\n      \"typed_parameters\": {\n        \"title\": \"string\",\n        \"enhanced_prompt\": \"string\",\n        \"context_layers\": \"array of objects {level:int, context:string}\"\n      },\n      \"json_output_specification\": {\n        \"title\": \"Descriptive title (max 50 chars)\",\n        \"enhanced_prompt\": \"Refined, actionable prompt (≤2x input length)\",\n        \"context_layers\": [\n          {\n            \"level\": 1,\n            \"context\": \"Primary high-level context\"\n          },\n          {\n            \"level\": 2,\n            \"context\": \"Supporting/operational context\"\n          }\n        ]\n      }\n    },\n    \"part_3_validation\": {\n      \"rulesforai_compliance_checklist\": [\n        \"Three-part canonical structure present\",\n        \"Explicit negation of conversational/ambiguous language\",\n        \"All output fields have typed parameter specifications\",\n        \"JSON output matches mandated format precisely\",\n        \"No implicit/hidden information allowed\",\n        \"No freeform narrative or dialogue\",\n        \"Strict actionable transformation logic only\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"part_1_interpretation\": {\n      \"intent_extracted\": \"Transform any incoming prompt into a structured JSON object containing a programmatically concise title, a maximally clarified and enhanced prompt formulation, and explicit, ordered hierarchical context layers.\",\n      \"assigned_role\": \"template_syntax_enforcer\",\n      \"goal_negation_pattern\": \"Do not provide summaries, unstructured responses, conversational language, or ambiguous advice. Do not surpass the three-part, strictly segmented template.\"\n    },\n    \"part_2_transformation\": {\n      \"role\": \"template_syntax_enforcer\",\n      \"input\": [\n        \"input_prompt:string\"\n      ],\n      \"process\": [\n        \"extract_core_message(input_prompt)\",\n        \"identify_key_themes(core_message)\",\n        \"generate_title(key_themes, max_length=50)\",\n        \"expand_context_layers(key_themes)\",\n        \"produce_enhanced_prompt(core_message, context_layers)\"\n      ],\n      \"constraints\": [\n        \"mandatory_three_part_structure\",\n        \"max_output_length_is_double_input\",\n        \"no_redundancy\",\n        \"no_conversational_elements\",\n        \"no hidden or implicit output fields\"\n      ],\n      \"requirements\": [\n        \"output_must_include_title\",\n        \"output_must_include_enhanced_prompt\",\n        \"output_must_include_context_layers_with_explicit_levels\",\n        \"json_structure_matches_specification\",\n        \"type_safety_for_all_fields\"\n      ],\n      \"output\": {\n        \"result_format\": \"structured_json\",\n        \"datatype\": \"object\",\n        \"fields\": {\n          \"title\": \"string\",\n          \"enhanced_prompt\": \"string\",\n          \"context_layers\": [\n            {\n              \"level\": \"int\",\n              \"context\": \"string\"\n            }\n          ]\n        }\n      }\n    },\n    \"part_3_validation\": {\n      \"rulesforai_compliance_checklist\": [\n        \"Mandatory three-part canonical structure enforced\",\n        \"Explicit conversational and ambiguity negation in interpretation directive\",\n        \"Direct, typed parameters declared\",\n        \"Process steps are strictly actionable and ordered\",\n        \"JSON output matches mandated schema without deviation\",\n        \"Zero freeform or meta-narrative content\",\n        \"All template fields and boundaries present with precision\"\n      ]\n    }\n  }\n}",
        }
      }
    }